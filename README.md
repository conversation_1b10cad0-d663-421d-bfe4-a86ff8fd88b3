# 猛男项目管理系统

一个专为团队协作设计的中文项目管理系统，支持任务生命周期管理、四象限优先级展示和每周计划功能。

## 🏗️ 技术栈

### 后端
- **框架**: Kotlin + Ktor
- **数据库**: MySQL 8.0+
- **ORM**: Exposed
- **认证**: JWT
- **构建工具**: Gradle

### 前端
- **框架**: React + TypeScript
- **UI库**: Ant Design
- **构建工具**: Vite
- **状态管理**: React Query

### 部署
- **容器化**: Docker + Docker Compose
- **数据库迁移**: Flyway

## 📋 核心功能

### 用户管理
- 普通用户注册登录（用户名+密码）
- 超级管理员（super/root）
- 用户头像和昵称管理
- 密码重置功能
- 操作日志记录

### 任务管理
- 完整生命周期：需求创建 → 分解任务 → 开发中 → 暂停 → 测试 → 待上线 → 上线
- 看板式展示
- 优先级管理（重要性 + 紧急程度）
- 任务分配和协作
- 测试阶段bug管理
- 操作留痕和时间跟踪

### 四象限展示
- 基于重要性和紧急程度的任务分类
- 可视化优先级管理
- 拖拽调整功能

### 每周计划
- 周计划制定和任务圈定
- 预估工时管理
- 紧急任务插入和计划调整
- 面向非技术人员的周报总结

## 🚀 快速开始

### 环境要求
- JDK 17+
- Node.js 18+
- MySQL 8.0+
- Docker (可选)

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd beefcake
```

2. 启动数据库
```bash
docker-compose up -d mysql
```

3. 启动后端
```bash
cd backend
./gradlew run
```

4. 启动前端
```bash
cd frontend
npm install
npm run dev
```

### Docker 部署

```bash
docker-compose up -d
```

## 📚 文档

- [API文档](docs/api.md)
- [数据库设计](docs/database.md)
- [部署指南](docs/deployment.md)
- [用户手册](docs/user-guide.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
