package com.beefcake

import com.beefcake.database.DatabaseFactory
import com.beefcake.plugins.*
import com.beefcake.utils.JwtUtils
import io.ktor.server.application.*
import io.ktor.server.netty.*

fun main(args: Array<String>) {
    EngineMain.main(args)
}

fun Application.module() {
    // 初始化JWT工具
    JwtUtils.init(environment.config)
    
    // 初始化数据库
    DatabaseFactory.init(environment.config)
    
    // 配置插件
    configureSerialization()
    configureSecurity()
    configureRouting()
    configureCORS(environment.config)
    configureCallLogging()
    configureStatusPages(environment.config)
}
