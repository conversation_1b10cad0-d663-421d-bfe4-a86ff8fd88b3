package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*

object TaskStatuses : LongIdTable("task_statuses") {
    val name = varchar("name", 50).uniqueIndex()
    val displayName = varchar("display_name", 100)
    val description = varchar("description", 500).nullable()
    val color = varchar("color", 20).default("#d9d9d9")
    val sortOrder = integer("sort_order").default(0)
    val isActive = bool("is_active").default(true)
    val isSystem = bool("is_system").default(false) // 系统内置状态不可删除
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}
