package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*

object Users : LongIdTable("users") {
    val username = varchar("username", 50).uniqueIndex()
    val password = varchar("password", 255)
    val nickname = varchar("nickname", 100).nullable()
    val avatarUrl = varchar("avatar_url", 500).nullable()
    val userType = enumerationByName("user_type", 20, UserType::class).default(UserType.NORMAL)
    val failedLoginCount = integer("failed_login_count").default(0)
    val status = enumerationByName("status", 20, UserStatus::class).default(UserStatus.ACTIVE)
    val lockedUntil = datetime("locked_until").nullable()
    val createdAt = datetime("created_at")
    val updatedAt = datetime("updated_at")
}

enum class UserType {
    NORMAL, SUPER_ADMIN
}

enum class UserStatus {
    ACTIVE, DISABLED, LOCKED
}