package com.beefcake.database.tables

import org.jetbrains.exposed.dao.id.LongIdTable
import org.jetbrains.exposed.sql.javatime.*
import java.time.LocalDateTime

object WeeklyPlans : LongIdTable("weekly_plans") {
    val weekStartDate = date("week_start_date")
    val weekEndDate = date("week_end_date")
    val planName = varchar("plan_name", 200).nullable()
    val status = enumerationByName("status", 20,WeeklyPlanStatus::class).default(WeeklyPlanStatus.PLANNING)
    val summary = text("summary").nullable()
    val createdBy = long("created_by").references(Users.id)
    val createdAt = datetime("created_at").default(LocalDateTime.now())
    val updatedAt = datetime("updated_at")
}

object WeeklyPlanTasks : LongIdTable("weekly_plan_tasks") {
    val weeklyPlanId = long("weekly_plan_id").references(WeeklyPlans.id)
    val taskId = long("task_id").references(Tasks.id)
    val plannedHours = decimal("planned_hours", 5, 2).nullable()
    val isEmergencyInsertion = bool("is_emergency_insertion").default(false)
    val createdAt = datetime("created_at")
}

enum class WeeklyPlanStatus {
    PLANNING, IN_PROGRESS, COMPLETED
}
