package com.beefcake.models

import com.beefcake.database.tables.Priority
import com.beefcake.database.tables.TaskStatus
import com.beefcake.database.tables.TaskType
import kotlinx.serialization.Serializable

@Serializable
data class Task(
    val id: Long,
    val title: String,
    val description: String?, // 技术描述
    val requirementId: Long, // 关联需求
    val taskType: TaskType,
    val status: TaskStatus,
    val priorityImportance: Priority,
    val priorityUrgency: Priority,
    val estimatedHours: Double?,
    val actualHours: Double?,
    val creatorId: Long,
    val assigneeId: Long?,
    val dueDate: String?,
    val startedAt: String?,
    val completedAt: String?,
    val createdAt: String,
    val updatedAt: String,
    val creator: User?,
    val assignee: User?,
    val requirement: Requirement?,
    val dependencies: List<TaskDependency>?, // 依赖关系
    val dependents: List<TaskDependency>?, // 被依赖关系
    val canStart: Boolean = true // 是否可以开始（依赖任务是否完成）
)

@Serializable
data class TaskDependency(
    val id: Long,
    val taskId: Long,
    val dependsOnTaskId: Long,
    val dependencyType: String, // FINISH_TO_START, START_TO_START, etc.
    val dependsOnTask: Task?
)

@Serializable
data class TaskCreateRequest(
    val title: String,
    val description: String? = null,
    val requirementId: Long,
    val taskType: TaskType = TaskType.DEVELOPMENT,
    val priorityImportance: Priority = Priority.MEDIUM,
    val priorityUrgency: Priority = Priority.MEDIUM,
    val estimatedHours: Double? = null,
    val assigneeId: Long? = null,
    val dueDate: String? = null,
    val dependencies: List<Long> = emptyList() // 依赖的任务ID列表
)

@Serializable
data class TaskUpdateRequest(
    val title: String? = null,
    val description: String? = null,
    val taskType: TaskType? = null,
    val status: TaskStatus? = null,
    val priorityImportance: Priority? = null,
    val priorityUrgency: Priority? = null,
    val estimatedHours: Double? = null,
    val actualHours: Double? = null,
    val assigneeId: Long? = null,
    val dueDate: String? = null
)

@Serializable
data class TaskListResponse(
    val tasks: List<Task>,
    val total: Int,
    val page: Int,
    val pageSize: Int
)

@Serializable
data class TaskStatusUpdateRequest(
    val status: TaskStatus,
    val comment: String? = null
)

@Serializable
data class TaskAssignRequest(
    val assigneeId: Long,
    val comment: String? = null
)

@Serializable
data class TaskQueryParams(
    val status: TaskStatus? = null,
    val assigneeId: Long? = null,
    val creatorId: Long? = null,
    val requirementId: Long? = null,
    val priorityImportance: Priority? = null,
    val priorityUrgency: Priority? = null,
    val parentTaskId: Long? = null,
    val search: String? = null, // 任务标题模糊搜索
    val requirementSearch: String? = null, // 关联需求模糊搜索
    val assigneeSearch: String? = null, // 负责人搜索
    val createdAtStart: String? = null, // 创建时间开始
    val createdAtEnd: String? = null, // 创建时间结束
    val page: Int = 1,
    val pageSize: Int = 20,
    val sortBy: String = "createdAt",
    val sortOrder: String = "desc"
)

// 四象限数据
@Serializable
data class QuadrantData(
    val urgentImportant: List<Task>,      // 紧急且重要
    val notUrgentImportant: List<Task>,   // 不紧急但重要
    val urgentNotImportant: List<Task>,   // 紧急但不重要
    val notUrgentNotImportant: List<Task> // 不紧急不重要
)

// 看板数据 - 按任务状态分组
@Serializable
data class KanbanData(
    val todo: List<Task>,        // 待开始
    val inProgress: List<Task>,  // 进行中
    val blocked: List<Task>,     // 阻塞
    val review: List<Task>,      // 评审中
    val testing: List<Task>,     // 测试中
    val done: List<Task>         // 已完成
)

// 需求看板数据 - 按需求状态分组
@Serializable
data class RequirementKanbanData(
    val draft: List<Requirement>,       // 草稿
    val review: List<Requirement>,      // 评审中
    val approved: List<Requirement>,    // 已批准
    val inProgress: List<Requirement>,  // 进行中
    val testing: List<Requirement>,     // 测试中
    val delivered: List<Requirement>    // 已交付
)

// 任务统计
@Serializable
data class TaskStatistics(
    val totalTasks: Int,
    val completedTasks: Int,
    val inProgressTasks: Int,
    val overdueTasks: Int,
    val tasksByStatus: Map<TaskStatus, Int>,
    val tasksByPriority: Map<String, Int>, // "HIGH_HIGH", "HIGH_MEDIUM", etc.
    val averageCompletionTime: Double?, // 平均完成时间（小时）
    val totalEstimatedHours: Double,
    val totalActualHours: Double
)
