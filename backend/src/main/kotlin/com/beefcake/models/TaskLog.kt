package com.beefcake.models

import com.beefcake.database.tables.ActionType
import kotlinx.serialization.Serializable

@Serializable
data class TaskLog(
    val id: Long,
    val taskId: Long,
    val userId: Long,
    val actionType: ActionType,
    val oldValue: String?,
    val newValue: String?,
    val comment: String?,
    val createdAt: String,
    val user: User?
)

@Serializable
data class TaskLogListResponse(
    val logs: List<TaskLog>,
    val total: Int,
    val page: Int,
    val pageSize: Int
)
