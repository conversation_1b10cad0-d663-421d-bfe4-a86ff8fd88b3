package com.beefcake.models

import kotlinx.serialization.Serializable

@Serializable
data class TaskStatusModel(
    val id: Long,
    val name: String,
    val displayName: String,
    val description: String?,
    val color: String,
    val sortOrder: Int,
    val isActive: Boolean,
    val isSystem: Boolean,
    val createdAt: String,
    val updatedAt: String
)

@Serializable
data class TaskStatusCreateRequest(
    val name: String,
    val displayName: String,
    val description: String? = null,
    val color: String = "#d9d9d9"
)

@Serializable
data class TaskStatusUpdateRequest(
    val displayName: String,
    val description: String? = null,
    val color: String,
    val isActive: Boolean
)

@Serializable
data class TaskStatusSortRequest(
    val statusIds: List<Long>
)

@Serializable
data class TaskStatusListResponse(
    val statuses: List<TaskStatusModel>,
    val total: Int
)
