package com.beefcake.models

import com.beefcake.database.tables.UserStatus
import com.beefcake.database.tables.UserType
import kotlinx.serialization.Serializable
import java.time.LocalDateTime

@Serializable
data class User(
    val id: Long,
    val username: String,
    val nickname: String?,
    val avatarUrl: String?,
    val userType: UserType,
    val status: UserStatus,
    val failedLoginCount: Int,
    val lockedUntil: String?, // 序列化为字符串
    val createdAt: String,
    val updatedAt: String
)

@Serializable
data class UserCreateRequest(
    val username: String,
    val password: String,
    val nickname: String? = null
)

@Serializable
data class UserLoginRequest(
    val username: String,
    val password: String
)

@Serializable
data class UserLoginResponse(
    val token: String,
    val user: User
)

@Serializable
data class UserUpdateRequest(
    val nickname: String?,
    val avatarUrl: String?
)

@Serializable
data class PasswordChangeRequest(
    val oldPassword: String,
    val newPassword: String
)

@Serializable
data class PasswordResetRequest(
    val userId: Long
)

@Serializable
data class UserListResponse(
    val users: List<User>,
    val total: Int,
    val page: Int,
    val pageSize: Int
)

@Serializable
data class UserAllResponse(
    val data: List<User>,
    val total: Int
)
