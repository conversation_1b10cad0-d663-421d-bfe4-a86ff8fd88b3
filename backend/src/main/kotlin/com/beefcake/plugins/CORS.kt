package com.beefcake.plugins

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.config.*
import io.ktor.server.plugins.cors.routing.*

fun Application.configureCORS(config: ApplicationConfig) {
    install(CORS) {
        allowMethod(HttpMethod.Options)
        allowMethod(HttpMethod.Put)
        allowMethod(HttpMethod.Delete)
        allowMethod(HttpMethod.Patch)
        allowHeader(HttpHeaders.Authorization)
        allowHeader(HttpHeaders.ContentType)
        allowCredentials = true
        
        // 开发环境允许所有来源
        if (config.propertyOrNull("app.environment")?.getString() == "development") {
            anyHost()
        } else {
            // 生产环境指定允许的域名
            allowHost("localhost:3000")
            allowHost("127.0.0.1:3000")
        }
    }
}
