package com.beefcake.plugins

import com.beefcake.utils.ErrorResponse
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.config.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.response.*
import org.slf4j.LoggerFactory

fun Application.configureStatusPages(config: ApplicationConfig) {
    val logger = LoggerFactory.getLogger("StatusPages")

    install(StatusPages) {
        exception<Throwable> { call, cause ->
            logger.error("未处理的异常", cause)
            call.respond(
                HttpStatusCode.InternalServerError,
                ErrorResponse(
                    message = "服务器内部错误",
                    code = 500,
                    details = if (config.propertyOrNull("app.environment")?.getString() == "development") {
                        cause.message
                    } else {
                        null
                    }
                )
            )
        }

        status(HttpStatusCode.NotFound) { call, status ->
            call.respond(
                status,
                ErrorResponse(
                    message = "请求的资源不存在",
                    code = status.value
                )
            )
        }

        status(HttpStatusCode.Unauthorized) { call, status ->
            call.respond(
                status,
                ErrorResponse(
                    message = "未授权访问",
                    code = status.value
                )
            )
        }
    }
}
