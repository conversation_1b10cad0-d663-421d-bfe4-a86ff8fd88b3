package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.Requirement
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class RequirementRepository {
    
    suspend fun create(
        title: String,
        businessDescription: String,
        acceptanceCriteria: String?,
        priorityImportance: Priority,
        priorityUrgency: Priority,
        estimatedValue: String?,
        targetUsers: String?,
        businessGoal: String?,
        creatorId: Long,
        assigneeId: Long?,
        expectedDeliveryDate: LocalDateTime?
    ): Requirement? = dbQuery {
        val insertStatement = Requirements.insert {
            it[Requirements.title] = title
            it[Requirements.businessDescription] = businessDescription
            it[Requirements.acceptanceCriteria] = acceptanceCriteria
            it[Requirements.priorityImportance] = priorityImportance
            it[Requirements.priorityUrgency] = priorityUrgency
            it[Requirements.estimatedValue] = estimatedValue
            it[Requirements.targetUsers] = targetUsers
            it[Requirements.businessGoal] = businessGoal
            it[Requirements.creatorId] = creatorId
            it[Requirements.assigneeId] = assigneeId
            it[Requirements.expectedDeliveryDate] = expectedDeliveryDate
            it[Requirements.createdAt] = LocalDateTime.now()
            it[Requirements.updatedAt] = LocalDateTime.now()
        }
        
        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToRequirement(it) }
    }
    
    suspend fun findById(id: Long): Requirement? = dbQuery {
        Requirements.selectAll().where { Requirements.id eq id }
            .map { resultRowToRequirement(it) }
            .singleOrNull()
    }
    
    suspend fun findAll(
        status: RequirementStatus? = null,
        assigneeId: Long? = null,
        creatorId: Long? = null,
        page: Int = 1,
        pageSize: Int = 20
    ): Pair<List<Requirement>, Int> = dbQuery {
        var query = Requirements.selectAll()
        
        // 添加过滤条件
        if (status != null) {
            query = query.andWhere { Requirements.status eq status }
        }
        if (assigneeId != null) {
            query = query.andWhere { Requirements.assigneeId eq assigneeId }
        }
        if (creatorId != null) {
            query = query.andWhere { Requirements.creatorId eq creatorId }
        }
        
        // 计算总数
        val total = query.count().toInt()
        
        // 添加分页和排序
        val requirements = query
            .orderBy(Requirements.createdAt, SortOrder.DESC)
            .limit(pageSize, offset = ((page - 1) * pageSize).toLong())
            .map { resultRowToRequirement(it) }
        
        requirements to total
    }
    
    suspend fun update(
        id: Long,
        title: String?,
        businessDescription: String?,
        acceptanceCriteria: String?,
        status: RequirementStatus?,
        priorityImportance: Priority?,
        priorityUrgency: Priority?,
        estimatedValue: String?,
        targetUsers: String?,
        businessGoal: String?,
        assigneeId: Long?,
        expectedDeliveryDate: LocalDateTime?,
        actualDeliveryDate: LocalDateTime?
    ): Boolean = dbQuery {
        Requirements.update({ Requirements.id eq id }) {
            if (title != null) it[Requirements.title] = title
            if (businessDescription != null) it[Requirements.businessDescription] = businessDescription
            if (acceptanceCriteria != null) it[Requirements.acceptanceCriteria] = acceptanceCriteria
            if (status != null) it[Requirements.status] = status
            if (priorityImportance != null) it[Requirements.priorityImportance] = priorityImportance
            if (priorityUrgency != null) it[Requirements.priorityUrgency] = priorityUrgency
            if (estimatedValue != null) it[Requirements.estimatedValue] = estimatedValue
            if (targetUsers != null) it[Requirements.targetUsers] = targetUsers
            if (businessGoal != null) it[Requirements.businessGoal] = businessGoal
            if (assigneeId != null) it[Requirements.assigneeId] = assigneeId
            if (expectedDeliveryDate != null) it[Requirements.expectedDeliveryDate] = expectedDeliveryDate
            if (actualDeliveryDate != null) it[Requirements.actualDeliveryDate] = actualDeliveryDate
            it[Requirements.updatedAt] = LocalDateTime.now()
        } > 0
    }
    
    suspend fun updateStatus(id: Long, status: RequirementStatus): Boolean = dbQuery {
        Requirements.update({ Requirements.id eq id }) {
            it[Requirements.status] = status
            it[Requirements.updatedAt] = LocalDateTime.now()
            
            // 如果状态是已交付，设置实际交付时间
            if (status == RequirementStatus.DELIVERED) {
                it[Requirements.actualDeliveryDate] = LocalDateTime.now()
            }
        } > 0
    }
    
    suspend fun delete(id: Long): Boolean = dbQuery {
        Requirements.deleteWhere { Requirements.id eq id } > 0
    }
    
    suspend fun findByStatus(status: RequirementStatus): List<Requirement> = dbQuery {
        Requirements.selectAll().where { Requirements.status eq status }
            .orderBy(Requirements.createdAt, SortOrder.DESC)
            .map { resultRowToRequirement(it) }
    }
    
    suspend fun getRequirementStatistics(): Map<String, Any> = dbQuery {
        val total = Requirements.selectAll().count().toInt()
        val delivered = Requirements.selectAll().where { Requirements.status eq RequirementStatus.DELIVERED }.count().toInt()
        val inProgress = Requirements.selectAll().where { 
            Requirements.status inList listOf(
                RequirementStatus.APPROVED,
                RequirementStatus.IN_PROGRESS,
                RequirementStatus.TESTING
            ) 
        }.count().toInt()
        
        val overdue = Requirements.selectAll().where { 
            (Requirements.expectedDeliveryDate less LocalDateTime.now()) and 
            (Requirements.status neq RequirementStatus.DELIVERED)
        }.count().toInt()
        
        mapOf(
            "totalRequirements" to total,
            "deliveredRequirements" to delivered,
            "inProgressRequirements" to inProgress,
            "overdueRequirements" to overdue
        )
    }
    
    private fun resultRowToRequirement(row: ResultRow): Requirement {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return Requirement(
            id = row[Requirements.id].value,
            title = row[Requirements.title],
            businessDescription = row[Requirements.businessDescription],
            acceptanceCriteria = row[Requirements.acceptanceCriteria],
            status = row[Requirements.status],
            priorityImportance = row[Requirements.priorityImportance],
            priorityUrgency = row[Requirements.priorityUrgency],
            estimatedValue = row[Requirements.estimatedValue],
            targetUsers = row[Requirements.targetUsers],
            businessGoal = row[Requirements.businessGoal],
            creatorId = row[Requirements.creatorId],
            assigneeId = row[Requirements.assigneeId],
            expectedDeliveryDate = row[Requirements.expectedDeliveryDate]?.format(formatter),
            actualDeliveryDate = row[Requirements.actualDeliveryDate]?.format(formatter),
            createdAt = row[Requirements.createdAt].format(formatter),
            updatedAt = row[Requirements.updatedAt].format(formatter),
            creator = null, // 需要时单独查询
            assignee = null, // 需要时单独查询
            tasks = null // 需要时单独查询
        )
    }
}
