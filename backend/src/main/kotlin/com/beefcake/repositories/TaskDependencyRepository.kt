package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.TaskDependencies
import com.beefcake.database.tables.Tasks
import com.beefcake.database.tables.DependencyType
import com.beefcake.models.TaskDependency
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import java.time.LocalDateTime

class TaskDependencyRepository {
    
    /**
     * 创建任务依赖关系
     */
    suspend fun createDependency(
        taskId: Long,
        dependsOnTaskId: Long,
        dependencyType: DependencyType = DependencyType.FINISH_TO_START
    ): TaskDependency? = dbQuery {
        // 检查是否已存在相同的依赖关系
        val existingDependency = TaskDependencies.selectAll()
            .where { (TaskDependencies.taskId eq taskId) and (TaskDependencies.dependsOnTaskId eq dependsOnTaskId) }
            .singleOrNull()
        
        if (existingDependency != null) {
            return@dbQuery null // 已存在相同依赖关系
        }
        
        // 检查是否会形成循环依赖
        if (wouldCreateCycle(taskId, dependsOnTaskId)) {
            return@dbQuery null // 会形成循环依赖
        }
        
        val insertStatement = TaskDependencies.insert {
            it[TaskDependencies.taskId] = taskId
            it[TaskDependencies.dependsOnTaskId] = dependsOnTaskId
            it[TaskDependencies.dependencyType] = dependencyType
            it[TaskDependencies.createdAt] = LocalDateTime.now()
        }
        
        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToTaskDependency(it) }
    }
    
    /**
     * 批量创建任务依赖关系
     */
    suspend fun createDependencies(
        taskId: Long,
        dependsOnTaskIds: List<Long>,
        dependencyType: DependencyType = DependencyType.FINISH_TO_START
    ): List<TaskDependency> = dbQuery {
        val createdDependencies = mutableListOf<TaskDependency>()
        
        for (dependsOnTaskId in dependsOnTaskIds) {
            val dependency = createDependency(taskId, dependsOnTaskId, dependencyType)
            if (dependency != null) {
                createdDependencies.add(dependency)
            }
        }
        
        createdDependencies
    }
    
    /**
     * 获取任务的所有依赖关系（当前任务依赖的其他任务）
     */
    suspend fun getDependenciesByTaskId(taskId: Long): List<TaskDependency> = dbQuery {
        TaskDependencies.selectAll()
            .where { TaskDependencies.taskId eq taskId }
            .map { resultRowToTaskDependency(it) }
    }
    
    /**
     * 获取任务的所有被依赖关系（其他任务依赖当前任务）
     */
    suspend fun getDependentsByTaskId(taskId: Long): List<TaskDependency> = dbQuery {
        TaskDependencies.selectAll()
            .where { TaskDependencies.dependsOnTaskId eq taskId }
            .map { resultRowToTaskDependency(it) }
    }
    
    /**
     * 删除特定的依赖关系
     */
    suspend fun deleteDependency(taskId: Long, dependsOnTaskId: Long): Boolean = dbQuery {
        TaskDependencies.deleteWhere { 
            (TaskDependencies.taskId eq taskId) and (TaskDependencies.dependsOnTaskId eq dependsOnTaskId) 
        } > 0
    }
    
    /**
     * 删除任务的所有依赖关系
     */
    suspend fun deleteAllDependenciesByTaskId(taskId: Long): Boolean = dbQuery {
        TaskDependencies.deleteWhere { 
            (TaskDependencies.taskId eq taskId) or (TaskDependencies.dependsOnTaskId eq taskId) 
        } > 0
    }
    
    /**
     * 检查是否会形成循环依赖
     */
    private suspend fun wouldCreateCycle(taskId: Long, dependsOnTaskId: Long): Boolean = dbQuery {
        // 如果 dependsOnTaskId 直接或间接依赖于 taskId，则会形成循环
        val visited = mutableSetOf<Long>()
        
        fun hasCycleDFS(currentTaskId: Long, targetTaskId: Long): Boolean {
            if (currentTaskId == targetTaskId) return true
            if (visited.contains(currentTaskId)) return false
            
            visited.add(currentTaskId)
            
            val dependencies = TaskDependencies.selectAll()
                .where { TaskDependencies.taskId eq currentTaskId }
                .map { it[TaskDependencies.dependsOnTaskId] }
            
            for (depTaskId in dependencies) {
                if (hasCycleDFS(depTaskId, targetTaskId)) {
                    return true
                }
            }
            
            return false
        }
        
        hasCycleDFS(dependsOnTaskId, taskId)
    }
    
    /**
     * 检查任务是否可以开始（所有依赖任务都已完成）
     */
    suspend fun canTaskStart(taskId: Long): Boolean = dbQuery {
        val dependencies = TaskDependencies.selectAll()
            .where { TaskDependencies.taskId eq taskId }
            .map { it[TaskDependencies.dependsOnTaskId] }
        
        if (dependencies.isEmpty()) return@dbQuery true
        
        // 检查所有依赖任务的状态
        val completedCount = Tasks.selectAll()
            .where { (Tasks.id inList dependencies) and (Tasks.status eq com.beefcake.database.tables.TaskStatus.DONE) }
            .count()
        
        completedCount.toInt() == dependencies.size
    }
    
    /**
     * 将ResultRow转换为TaskDependency对象
     */
    private fun resultRowToTaskDependency(row: ResultRow): TaskDependency {
        return TaskDependency(
            id = row[TaskDependencies.id].value,
            taskId = row[TaskDependencies.taskId],
            dependsOnTaskId = row[TaskDependencies.dependsOnTaskId],
            dependencyType = row[TaskDependencies.dependencyType].toString(),
            dependsOnTask = null // 需要时单独查询
        )
    }
}
