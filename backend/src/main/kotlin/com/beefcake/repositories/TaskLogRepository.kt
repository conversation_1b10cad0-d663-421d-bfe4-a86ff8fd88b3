package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.ActionType
import com.beefcake.database.tables.TaskLogs
import com.beefcake.models.TaskLog
import org.jetbrains.exposed.sql.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class TaskLogRepository {
    
    suspend fun create(
        taskId: Long,
        userId: Long,
        actionType: ActionType,
        oldValue: String? = null,
        newValue: String? = null,
        comment: String? = null
    ): TaskLog? = dbQuery {
        val insertStatement = TaskLogs.insert {
            it[TaskLogs.taskId] = taskId
            it[TaskLogs.userId] = userId
            it[TaskLogs.actionType] = actionType
            it[TaskLogs.oldValue] = oldValue
            it[TaskLogs.newValue] = newValue
            it[TaskLogs.comment] = comment
            it[TaskLogs.createdAt] = LocalDateTime.now()
        }
        
        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToTaskLog(it) }
    }
    
    suspend fun findByTaskId(taskId: Long): List<TaskLog> = dbQuery {
        TaskLogs.selectAll().where { TaskLogs.taskId eq taskId }
            .orderBy(TaskLogs.createdAt, SortOrder.DESC)
            .map { resultRowToTaskLog(it) }
    }

    suspend fun findByUserId(userId: Long, limit: Int = 50): List<TaskLog> = dbQuery {
        TaskLogs.selectAll().where { TaskLogs.userId eq userId }
            .orderBy(TaskLogs.createdAt, SortOrder.DESC)
            .limit(limit)
            .map { resultRowToTaskLog(it) }
    }
    
    suspend fun findAll(page: Int = 1, pageSize: Int = 20): Pair<List<TaskLog>, Int> = dbQuery {
        val total = TaskLogs.selectAll().count().toInt()
        val logs = TaskLogs.selectAll()
            .orderBy(TaskLogs.createdAt, SortOrder.DESC)
            .limit(pageSize, offset = ((page - 1) * pageSize).toLong())
            .map { resultRowToTaskLog(it) }
        
        logs to total
    }
    
    private fun resultRowToTaskLog(row: ResultRow): TaskLog {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return TaskLog(
            id = row[TaskLogs.id].value,
            taskId = row[TaskLogs.taskId],
            userId = row[TaskLogs.userId],
            actionType = row[TaskLogs.actionType],
            oldValue = row[TaskLogs.oldValue],
            newValue = row[TaskLogs.newValue],
            comment = row[TaskLogs.comment],
            createdAt = row[TaskLogs.createdAt].format(formatter),
            user = null // 需要时单独查询
        )
    }
}
