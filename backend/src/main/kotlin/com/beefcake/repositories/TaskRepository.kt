package com.beefcake.repositories

import com.beefcake.database.DatabaseFactory.dbQuery
import com.beefcake.database.tables.*
import com.beefcake.models.Task
import com.beefcake.models.TaskDependency
import com.beefcake.models.TaskQueryParams
import com.beefcake.models.Requirement
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.like
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class TaskRepository {
    
    suspend fun create(
        title: String,
        description: String?,
        requirementId: Long,
        taskType: TaskType,
        priorityImportance: Priority,
        priorityUrgency: Priority,
        estimatedHours: Double?,
        creatorId: Long,
        assigneeId: Long?,
        dueDate: LocalDateTime?
    ): Task? = dbQuery {
        val insertStatement = Tasks.insert {
            it[Tasks.title] = title
            it[Tasks.description] = description
            it[Tasks.requirementId] = requirementId
            it[Tasks.taskType] = taskType
            it[Tasks.priorityImportance] = priorityImportance
            it[Tasks.priorityUrgency] = priorityUrgency
            it[Tasks.estimatedHours] = estimatedHours?.toBigDecimal()
            it[Tasks.creatorId] = creatorId
            it[Tasks.assigneeId] = assigneeId
            it[Tasks.dueDate] = dueDate
            it[Tasks.createdAt] = LocalDateTime.now()
            it[Tasks.updatedAt] = LocalDateTime.now()
        }
        
        insertStatement.resultedValues?.singleOrNull()?.let { resultRowToTask(it) }
    }
    
    suspend fun findById(id: Long): Task? = dbQuery {
        Tasks.selectAll().where { Tasks.id eq id }
            .map { resultRowToTask(it) }
            .singleOrNull()
    }
    
    suspend fun update(
        id: Long,
        title: String?,
        description: String?,
        taskType: TaskType?,
        status: TaskStatus?,
        priorityImportance: Priority?,
        priorityUrgency: Priority?,
        estimatedHours: Double?,
        actualHours: Double?,
        assigneeId: Long?,
        dueDate: LocalDateTime?
    ): Boolean = dbQuery {
        Tasks.update({ Tasks.id eq id }) {
            if (title != null) it[Tasks.title] = title
            if (description != null) it[Tasks.description] = description
            if (taskType != null) it[Tasks.taskType] = taskType
            if (status != null) it[Tasks.status] = status
            if (priorityImportance != null) it[Tasks.priorityImportance] = priorityImportance
            if (priorityUrgency != null) it[Tasks.priorityUrgency] = priorityUrgency
            if (estimatedHours != null) it[Tasks.estimatedHours] = estimatedHours.toBigDecimal()
            if (actualHours != null) it[Tasks.actualHours] = actualHours.toBigDecimal()
            if (assigneeId != null) it[Tasks.assigneeId] = assigneeId
            if (dueDate != null) it[Tasks.dueDate] = dueDate
            it[Tasks.updatedAt] = LocalDateTime.now()
        } > 0
    }
    
    suspend fun updateStatus(id: Long, status: TaskStatus): Boolean = dbQuery {
        val now = LocalDateTime.now()

        Tasks.update({ Tasks.id eq id }) {
            it[Tasks.status] = status
            it[Tasks.updatedAt] = now

            // 根据状态更新时间戳
            when (status) {
                TaskStatus.IN_PROGRESS -> {
                    it[Tasks.startedAt] = now
                }
                TaskStatus.DONE -> {
                    it[Tasks.completedAt] = now
                }
                else -> {}
            }
        } > 0
    }
    
    suspend fun delete(id: Long): Boolean = dbQuery {
        Tasks.deleteWhere { Tasks.id eq id } > 0
    }
    
    suspend fun findAll(params: TaskQueryParams): Pair<List<Task>, Int> = dbQuery {
        var query = Tasks.selectAll()
        
        // 添加过滤条件
        if (params.status != null) {
            query = query.andWhere { Tasks.status eq params.status }
        }
        if (params.assigneeId != null) {
            query = query.andWhere { Tasks.assigneeId eq params.assigneeId }
        }
        if (params.creatorId != null) {
            query = query.andWhere { Tasks.creatorId eq params.creatorId }
        }
        if (params.priorityImportance != null) {
            query = query.andWhere { Tasks.priorityImportance eq params.priorityImportance }
        }
        if (params.priorityUrgency != null) {
            query = query.andWhere { Tasks.priorityUrgency eq params.priorityUrgency }
        }
        // 移除 parentTaskId 过滤，因为新设计中没有这个字段
        if (!params.search.isNullOrBlank()) {
            query = query.andWhere {
                (Tasks.title like "%${params.search}%") or
                (Tasks.description like "%${params.search}%")
            }
        }
        
        // 计算总数
        val total = query.count().toInt()
        
        // 添加排序
        val sortColumn = when (params.sortBy) {
            "title" -> Tasks.title
            "status" -> Tasks.status
            "priority" -> Tasks.priorityImportance
            "dueDate" -> Tasks.dueDate
            "updatedAt" -> Tasks.updatedAt
            else -> Tasks.createdAt
        }
        
        query = if (params.sortOrder.lowercase() == "asc") {
            query.orderBy(sortColumn, SortOrder.ASC)
        } else {
            query.orderBy(sortColumn, SortOrder.DESC)
        }
        
        // 添加分页
        val tasks = query
            .limit(params.pageSize, offset = ((params.page - 1) * params.pageSize).toLong())
            .map { resultRowToTask(it) }
        
        tasks to total
    }
    
    suspend fun findByStatus(status: TaskStatus): List<Task> = dbQuery {
        Tasks.selectAll().where { Tasks.status eq status }
            .orderBy(Tasks.createdAt, SortOrder.DESC)
            .map { resultRowToTask(it) }
    }

    suspend fun findByRequirementId(requirementId: Long): List<Task> = dbQuery {
        Tasks.selectAll().where { Tasks.requirementId eq requirementId }
            .orderBy(Tasks.createdAt, SortOrder.ASC)
            .map { resultRowToTask(it) }
    }
    
    suspend fun findOverdueTasks(): List<Task> = dbQuery {
        val now = LocalDateTime.now()
        Tasks.selectAll().where {
            (Tasks.dueDate less now) and
            (Tasks.status neq TaskStatus.DONE)
        }
        .orderBy(Tasks.dueDate, SortOrder.ASC)
        .map { resultRowToTask(it) }
    }

    /**
     * 获取带有依赖关系的任务详情
     */
    suspend fun findByIdWithDependencies(id: Long): Task? = dbQuery {
        val task = Tasks.selectAll().where { Tasks.id eq id }
            .map { resultRowToTask(it) }
            .singleOrNull()

        if (task != null) {
            val dependencies = getDependenciesByTaskId(id)
            val dependents = getDependentsByTaskId(id)
            val canStart = canTaskStart(id)

            task.copy(
                dependencies = dependencies,
                dependents = dependents,
                canStart = canStart
            )
        } else {
            null
        }
    }

    /**
     * 获取任务的依赖关系（当前任务依赖的其他任务）
     */
    private suspend fun getDependenciesByTaskId(taskId: Long): List<TaskDependency> = dbQuery {
        TaskDependencies.join(
            Tasks,
            JoinType.INNER,
            additionalConstraint = { TaskDependencies.dependsOnTaskId eq Tasks.id }
        ).selectAll()
            .where { TaskDependencies.taskId eq taskId }
            .map { row ->
                TaskDependency(
                    id = row[TaskDependencies.id].value,
                    taskId = row[TaskDependencies.taskId],
                    dependsOnTaskId = row[TaskDependencies.dependsOnTaskId],
                    dependencyType = row[TaskDependencies.dependencyType].toString(),
                    dependsOnTask = resultRowToTask(row)
                )
            }
    }

    /**
     * 获取任务的被依赖关系（其他任务依赖当前任务）
     */
    private suspend fun getDependentsByTaskId(taskId: Long): List<TaskDependency> = dbQuery {
        TaskDependencies.join(
            Tasks,
            JoinType.INNER,
            additionalConstraint = { TaskDependencies.taskId eq Tasks.id }
        ).selectAll()
            .where { TaskDependencies.dependsOnTaskId eq taskId }
            .map { row ->
                TaskDependency(
                    id = row[TaskDependencies.id].value,
                    taskId = row[TaskDependencies.taskId],
                    dependsOnTaskId = row[TaskDependencies.dependsOnTaskId],
                    dependencyType = row[TaskDependencies.dependencyType].toString(),
                    dependsOnTask = resultRowToTask(row)
                )
            }
    }

    /**
     * 检查任务是否可以开始（所有依赖任务都已完成）
     */
    private suspend fun canTaskStart(taskId: Long): Boolean = dbQuery {
        val dependencies = TaskDependencies.selectAll()
            .where { TaskDependencies.taskId eq taskId }
            .map { it[TaskDependencies.dependsOnTaskId] }

        if (dependencies.isEmpty()) return@dbQuery true

        // 检查所有依赖任务的状态
        val completedCount = Tasks.selectAll()
            .where { (Tasks.id inList dependencies) and (Tasks.status eq TaskStatus.DONE) }
            .count()

        completedCount.toInt() == dependencies.size
    }
    
    suspend fun getTaskStatistics(): Map<String, Any> = dbQuery {
        val total = Tasks.selectAll().count().toInt()
        val completed = Tasks.selectAll().where { Tasks.status eq TaskStatus.DONE }.count().toInt()
        val inProgress = Tasks.selectAll().where {
            Tasks.status inList listOf(
                TaskStatus.IN_PROGRESS,
                TaskStatus.REVIEW,
                TaskStatus.TESTING
            )
        }.count().toInt()

        val overdue = Tasks.selectAll().where {
            (Tasks.dueDate less LocalDateTime.now()) and
            (Tasks.status neq TaskStatus.DONE)
        }.count().toInt()
        
        mapOf(
            "totalTasks" to total,
            "completedTasks" to completed,
            "inProgressTasks" to inProgress,
            "overdueTasks" to overdue
        )
    }
    
    private fun resultRowToTask(row: ResultRow): Task {
        val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
        return Task(
            id = row[Tasks.id].value,
            title = row[Tasks.title],
            description = row[Tasks.description],
            requirementId = row[Tasks.requirementId],
            taskType = row[Tasks.taskType],
            status = row[Tasks.status],
            priorityImportance = row[Tasks.priorityImportance],
            priorityUrgency = row[Tasks.priorityUrgency],
            estimatedHours = row[Tasks.estimatedHours]?.toDouble(),
            actualHours = row[Tasks.actualHours]?.toDouble(),
            creatorId = row[Tasks.creatorId],
            assigneeId = row[Tasks.assigneeId],
            dueDate = row[Tasks.dueDate]?.format(formatter),
            startedAt = row[Tasks.startedAt]?.format(formatter),
            completedAt = row[Tasks.completedAt]?.format(formatter),
            createdAt = row[Tasks.createdAt].format(formatter),
            updatedAt = row[Tasks.updatedAt].format(formatter),
            creator = null, // 需要时单独查询
            assignee = null, // 需要时单独查询
            requirement = null, // 需要时单独查询
            dependencies = null, // 需要时单独查询
            dependents = null, // 需要时单独查询
            canStart = true // 默认可以开始，需要时计算
        )
    }

    /**
     * 获取带有完整关联信息的任务列表
     */
    suspend fun findAllWithRelations(params: TaskQueryParams): Pair<List<Task>, Int> = dbQuery {
        // 构建基础查询
        val query = Tasks.selectAll()

        // 应用过滤条件
        var whereClause: Op<Boolean>? = null

        if (params.status != null) {
            whereClause = whereClause?.and(Tasks.status eq params.status) ?: (Tasks.status eq params.status)
        }

        if (params.assigneeId != null) {
            whereClause = whereClause?.and(Tasks.assigneeId eq params.assigneeId) ?: (Tasks.assigneeId eq params.assigneeId)
        }

        if (params.creatorId != null) {
            whereClause = whereClause?.and(Tasks.creatorId eq params.creatorId) ?: (Tasks.creatorId eq params.creatorId)
        }

        if (params.requirementId != null) {
            whereClause = whereClause?.and(Tasks.requirementId eq params.requirementId) ?: (Tasks.requirementId eq params.requirementId)
        }

        if (params.priorityImportance != null) {
            whereClause = whereClause?.and(Tasks.priorityImportance eq params.priorityImportance) ?: (Tasks.priorityImportance eq params.priorityImportance)
        }

        if (params.priorityUrgency != null) {
            whereClause = whereClause?.and(Tasks.priorityUrgency eq params.priorityUrgency) ?: (Tasks.priorityUrgency eq params.priorityUrgency)
        }

        // 任务标题模糊搜索
        if (!params.search.isNullOrBlank()) {
            whereClause = whereClause?.and(Tasks.title like "%${params.search}%") ?: (Tasks.title like "%${params.search}%")
        }

        // TODO: 暂时注释掉复杂搜索功能，等修复操作符问题后再启用
        // 关联需求模糊搜索和负责人搜索、时间范围搜索将在后续版本中实现

        if (whereClause != null) {
            query.where(whereClause)
        }

        // 计算总数
        val total = query.count().toInt()

        // 应用排序
        val sortOrder = if (params.sortOrder == "asc") SortOrder.ASC else SortOrder.DESC
        val orderBy = when (params.sortBy) {
            "title" -> Tasks.title to sortOrder
            "status" -> Tasks.status to sortOrder
            "priorityImportance" -> Tasks.priorityImportance to sortOrder
            "priorityUrgency" -> Tasks.priorityUrgency to sortOrder
            "dueDate" -> Tasks.dueDate to sortOrder
            "updatedAt" -> Tasks.updatedAt to sortOrder
            else -> Tasks.createdAt to sortOrder
        }

        // 应用分页和排序
        val tasks = query
            .orderBy(orderBy.first, orderBy.second)
            .limit(params.pageSize, ((params.page - 1) * params.pageSize).toLong())
            .map { row ->
                val task = resultRowToTask(row)
                // 加载关联的需求信息
                val requirement = loadRequirementForTask(task.requirementId)
                // TODO: 暂时注释掉用户信息加载，后续实现
                // val creator = loadUserForTask(task.creatorId)
                // val assignee = if (task.assigneeId != null) loadUserForTask(task.assigneeId) else null
                // 加载依赖关系信息
                val dependencies = getDependenciesByTaskId(task.id)
                val dependents = getDependentsByTaskId(task.id)
                val canStart = canTaskStart(task.id)

                task.copy(
                    requirement = requirement,
                    // creator = creator,
                    // assignee = assignee,
                    dependencies = dependencies,
                    dependents = dependents,
                    canStart = canStart
                )
            }

        tasks to total
    }

    /**
     * 加载任务关联的需求信息
     */
    private suspend fun loadRequirementForTask(requirementId: Long): Requirement? = dbQuery {
        Requirements.selectAll().where { Requirements.id eq requirementId }
            .map { row ->
                val formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME
                Requirement(
                    id = row[Requirements.id].value,
                    title = row[Requirements.title],
                    businessDescription = row[Requirements.businessDescription],
                    acceptanceCriteria = row[Requirements.acceptanceCriteria],
                    status = row[Requirements.status],
                    priorityImportance = row[Requirements.priorityImportance],
                    priorityUrgency = row[Requirements.priorityUrgency],
                    estimatedValue = row[Requirements.estimatedValue],
                    targetUsers = row[Requirements.targetUsers],
                    businessGoal = row[Requirements.businessGoal],
                    creatorId = row[Requirements.creatorId],
                    assigneeId = row[Requirements.assigneeId],
                    expectedDeliveryDate = row[Requirements.expectedDeliveryDate]?.format(formatter),
                    actualDeliveryDate = row[Requirements.actualDeliveryDate]?.format(formatter),
                    createdAt = row[Requirements.createdAt].format(formatter),
                    updatedAt = row[Requirements.updatedAt].format(formatter),
                    creator = null,
                    assignee = null,
                    tasks = null
                )
            }
            .singleOrNull()
    }



    /**
     * 检查任务是否可以删除
     * 被下游依赖的任务不允许删除
     */
    suspend fun canDeleteTask(taskId: Long): Pair<Boolean, String?> = dbQuery {
        // 检查是否有其他任务依赖此任务
        val dependentCount = TaskDependencies.selectAll()
            .where { TaskDependencies.dependsOnTaskId eq taskId }
            .count()

        if (dependentCount > 0) {
            return@dbQuery false to "该任务被其他任务依赖，无法删除"
        }

        return@dbQuery true to null
    }
}
