package com.beefcake.routes

import com.beefcake.models.UserCreateRequest
import com.beefcake.models.UserLoginRequest
import com.beefcake.services.UserService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondSuccess
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Route.authRoutes() {
    val userService = UserService()
    val logger = LoggerFactory.getLogger("AuthRoutes")
    
    route("/auth") {
        post("/register") {
            try {
                val request = call.receive<UserCreateRequest>()
                
                val result = userService.register(request)
                if (result.isSuccess) {
                    call.respondSuccess(result.getOrNull()!!, "注册成功", HttpStatusCode.Created)
                } else {
                    call.respondError(result.exceptionOrNull()?.message ?: "注册失败")
                }
            } catch (e: Exception) {
                logger.error("用户注册异常", e)
                call.respondError("请求格式错误")
            }
        }
        
        post("/login") {
            try {
                val request = call.receive<UserLoginRequest>()
                
                val result = userService.login(request)
                if (result.isSuccess) {
                    call.respondSuccess(result.getOrNull()!!, "登录成功")
                } else {
                    call.respondError(result.exceptionOrNull()?.message ?: "登录失败", HttpStatusCode.Unauthorized)
                }
            } catch (e: Exception) {
                logger.error("用户登录异常", e)
                call.respondError("请求格式错误")
            }
        }
    }
}
