package com.beefcake.routes

import com.beefcake.database.tables.Priority
import com.beefcake.database.tables.TaskStatus
import com.beefcake.models.*
import com.beefcake.services.TaskService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondNotFound
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Route.taskRoutes() {
    val taskService = TaskService()
    val logger = LoggerFactory.getLogger("TaskRoutes")
    
    route("/tasks") {
        authenticate("auth-jwt") {
            // 创建任务
            post {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@post
                    }
                    
                    val request = call.receive<TaskCreateRequest>()
                    val result = taskService.createTask(request, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "任务创建成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "任务创建失败")
                    }
                } catch (e: Exception) {
                    logger.error("创建任务异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 获取任务列表
            get {
                try {
                    val params = TaskQueryParams(
                        status = call.request.queryParameters["status"]?.let { TaskStatus.valueOf(it) },
                        assigneeId = call.request.queryParameters["assigneeId"]?.toLongOrNull(),
                        creatorId = call.request.queryParameters["creatorId"]?.toLongOrNull(),
                        priorityImportance = call.request.queryParameters["priorityImportance"]?.let { Priority.valueOf(it) },
                        priorityUrgency = call.request.queryParameters["priorityUrgency"]?.let { Priority.valueOf(it) },
                        parentTaskId = call.request.queryParameters["parentTaskId"]?.toLongOrNull(),
                        search = call.request.queryParameters["search"],
                        page = call.request.queryParameters["page"]?.toIntOrNull() ?: 1,
                        pageSize = call.request.queryParameters["pageSize"]?.toIntOrNull() ?: 20,
                        sortBy = call.request.queryParameters["sortBy"] ?: "createdAt",
                        sortOrder = call.request.queryParameters["sortOrder"] ?: "desc"
                    )
                    
                    val result = taskService.getTaskList(params)
                    call.respondSuccess(result, "获取任务列表成功")
                } catch (e: Exception) {
                    logger.error("获取任务列表异常", e)
                    call.respondError("获取任务列表失败")
                }
            }

            // 获取可选的上游任务列表（用于创建任务时选择依赖）
            get("/upstream-options") {
                try {
                    val requirementId = call.request.queryParameters["requirementId"]?.toLongOrNull()
                    if (requirementId == null) {
                        call.respondError("需求ID参数必填")
                        return@get
                    }

                    val result = taskService.getUpstreamTaskOptions(requirementId)
                    call.respondSuccess(result, "获取上游任务选项成功")
                } catch (e: Exception) {
                    logger.error("获取上游任务选项异常", e)
                    call.respondError("获取上游任务选项失败")
                }
            }

            // 获取单个任务
            get("/{id}") {
                try {
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("任务ID格式错误")
                        return@get
                    }
                    
                    val task = taskService.getTaskById(id)
                    if (task != null) {
                        call.respondSuccess(task, "获取任务成功")
                    } else {
                        call.respondNotFound("任务不存在")
                    }
                } catch (e: Exception) {
                    logger.error("获取任务异常", e)
                    call.respondError("获取任务失败")
                }
            }
            
            // 更新任务
            put("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@put
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("任务ID格式错误")
                        return@put
                    }
                    
                    val request = call.receive<TaskUpdateRequest>()
                    val result = taskService.updateTask(id, request, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "任务更新成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "任务更新失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新任务异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 更新任务状态
            patch("/{id}/status") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@patch
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("任务ID格式错误")
                        return@patch
                    }
                    
                    val request = call.receive<TaskStatusChangeRequest>()
                    val result = taskService.updateTaskStatus(id, request.status, userId, request.comment)
                    
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "任务状态更新成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "任务状态更新失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新任务状态异常", e)
                    call.respondError("请求格式错误")
                }
            }
            
            // 删除任务
            delete("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userId = principal?.getClaim("userId", Long::class)
                    
                    if (userId == null) {
                        call.respondUnauthorized()
                        return@delete
                    }
                    
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("任务ID格式错误")
                        return@delete
                    }
                    
                    val result = taskService.deleteTask(id, userId)
                    
                    if (result.isSuccess) {
                        call.respondSuccess("任务删除成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "任务删除失败")
                    }
                } catch (e: Exception) {
                    logger.error("删除任务异常", e)
                    call.respondError("删除任务失败")
                }
            }
            
            // 获取任务日志
            get("/{id}/logs") {
                try {
                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("任务ID格式错误")
                        return@get
                    }
                    
                    val logs = taskService.getTaskLogs(id)
                    call.respondSuccess(logs, "获取任务日志成功")
                } catch (e: Exception) {
                    logger.error("获取任务日志异常", e)
                    call.respondError("获取任务日志失败")
                }
            }
            
            // 获取看板数据
            get("/kanban") {
                try {
                    val kanbanData = taskService.getKanbanData()
                    call.respondSuccess(kanbanData, "获取看板数据成功")
                } catch (e: Exception) {
                    logger.error("获取看板数据异常", e)
                    call.respondError("获取看板数据失败")
                }
            }
            
            // 获取四象限数据
            get("/quadrant") {
                try {
                    val quadrantData = taskService.getQuadrantData()
                    call.respondSuccess(quadrantData, "获取四象限数据成功")
                } catch (e: Exception) {
                    logger.error("获取四象限数据异常", e)
                    call.respondError("获取四象限数据失败")
                }
            }
            
            // 获取任务统计
            get("/statistics") {
                try {
                    val statistics = taskService.getTaskStatistics()
                    call.respondSuccess(statistics, "获取任务统计成功")
                } catch (e: Exception) {
                    logger.error("获取任务统计异常", e)
                    call.respondError("获取任务统计失败")
                }
            }
        }
    }
}
