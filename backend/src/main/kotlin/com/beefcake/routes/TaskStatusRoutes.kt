package com.beefcake.routes

import com.beefcake.database.tables.UserType
import com.beefcake.models.*
import com.beefcake.services.TaskStatusService
import com.beefcake.utils.ResponseUtils.respondError
import com.beefcake.utils.ResponseUtils.respondForbidden
import com.beefcake.utils.ResponseUtils.respondSuccess
import com.beefcake.utils.ResponseUtils.respondUnauthorized
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.request.*
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory

fun Route.taskStatusRoutes() {
    val taskStatusService = TaskStatusService()
    val logger = LoggerFactory.getLogger("TaskStatusRoutes")

    route("/admin/task-statuses") {
        authenticate("auth-jwt") {
            // 获取所有任务状态
            get {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)

                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@get
                    }

                    val includeInactive = call.request.queryParameters["includeInactive"]?.toBoolean() ?: false
                    val result = taskStatusService.getAllTaskStatuses(includeInactive)

                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "获取任务状态列表成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "获取任务状态列表失败")
                    }
                } catch (e: Exception) {
                    logger.error("获取任务状态列表异常", e)
                    call.respondError("获取任务状态列表失败")
                }
            }

            // 获取单个任务状态
            get("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)

                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@get
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的状态ID")
                        return@get
                    }

                    val result = taskStatusService.getTaskStatusById(id)
                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "获取任务状态成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "获取任务状态失败")
                    }
                } catch (e: Exception) {
                    logger.error("获取任务状态异常", e)
                    call.respondError("获取任务状态失败")
                }
            }

            // 创建任务状态
            post {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)

                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@post
                    }

                    val request = call.receive<TaskStatusCreateRequest>()
                    val result = taskStatusService.createTaskStatus(request)

                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "创建任务状态成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "创建任务状态失败")
                    }
                } catch (e: Exception) {
                    logger.error("创建任务状态异常", e)
                    call.respondError("请求格式错误")
                }
            }

            // 更新任务状态
            put("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)

                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@put
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的状态ID")
                        return@put
                    }

                    val request = call.receive<TaskStatusUpdateRequest>()
                    val result = taskStatusService.updateTaskStatus(id, request)

                    if (result.isSuccess) {
                        call.respondSuccess(result.getOrNull()!!, "更新任务状态成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "更新任务状态失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新任务状态异常", e)
                    call.respondError("请求格式错误")
                }
            }

            // 删除任务状态
            delete("/{id}") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)

                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@delete
                    }

                    val id = call.parameters["id"]?.toLongOrNull()
                    if (id == null) {
                        call.respondError("无效的状态ID")
                        return@delete
                    }

                    val result = taskStatusService.deleteTaskStatus(id)
                    if (result.isSuccess) {
                        call.respondSuccess(Unit, "删除任务状态成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "删除任务状态失败")
                    }
                } catch (e: Exception) {
                    logger.error("删除任务状态异常", e)
                    call.respondError("删除任务状态失败")
                }
            }

            // 更新状态排序
            post("/sort") {
                try {
                    val principal = call.principal<JWTPrincipal>()
                    val userType = principal?.getClaim("userType", String::class)

                    if (userType != UserType.SUPER_ADMIN.name) {
                        call.respondForbidden("权限不足")
                        return@post
                    }

                    val request = call.receive<TaskStatusSortRequest>()
                    val result = taskStatusService.updateStatusOrder(request.statusIds)

                    if (result.isSuccess) {
                        call.respondSuccess(Unit, "更新状态排序成功")
                    } else {
                        call.respondError(result.exceptionOrNull()?.message ?: "更新状态排序失败")
                    }
                } catch (e: Exception) {
                    logger.error("更新状态排序异常", e)
                    call.respondError("请求格式错误")
                }
            }
        }
    }
}
