package com.beefcake.services

import com.beefcake.models.*
import com.beefcake.repositories.TaskStatusRepository
import org.slf4j.LoggerFactory

class TaskStatusService {
    private val taskStatusRepository = TaskStatusRepository()
    private val logger = LoggerFactory.getLogger(TaskStatusService::class.java)

    /**
     * 获取所有任务状态
     */
    suspend fun getAllTaskStatuses(includeInactive: Boolean = false): Result<TaskStatusListResponse> {
        return try {
            val statuses = taskStatusRepository.findAll(includeInactive)
            Result.success(TaskStatusListResponse(statuses, statuses.size))
        } catch (e: Exception) {
            logger.error("获取任务状态列表失败", e)
            Result.failure(e)
        }
    }

    /**
     * 根据ID获取任务状态
     */
    suspend fun getTaskStatusById(id: Long): Result<TaskStatusModel> {
        return try {
            val status = taskStatusRepository.findById(id)
                ?: return Result.failure(Exception("任务状态不存在"))
            Result.success(status)
        } catch (e: Exception) {
            logger.error("获取任务状态失败", e)
            Result.failure(e)
        }
    }

    /**
     * 创建任务状态
     */
    suspend fun createTaskStatus(request: TaskStatusCreateRequest): Result<TaskStatusModel> {
        return try {
            // 验证状态名称格式
            if (!isValidStatusName(request.name)) {
                return Result.failure(Exception("状态名称只能包含大写字母和下划线"))
            }

            // 检查状态名称是否已存在
            val existingStatus = taskStatusRepository.findByName(request.name)
            if (existingStatus != null) {
                return Result.failure(Exception("状态名称已存在"))
            }

            val status = taskStatusRepository.create(request)
                ?: return Result.failure(Exception("创建任务状态失败"))

            logger.info("创建任务状态成功: ${status.name}")
            Result.success(status)
        } catch (e: Exception) {
            logger.error("创建任务状态失败", e)
            Result.failure(e)
        }
    }

    /**
     * 更新任务状态
     */
    suspend fun updateTaskStatus(id: Long, request: TaskStatusUpdateRequest): Result<TaskStatusModel> {
        return try {
            // 检查状态是否存在
            val existingStatus = taskStatusRepository.findById(id)
                ?: return Result.failure(Exception("任务状态不存在"))

            // 系统状态不允许修改某些属性
            if (existingStatus.isSystem && !request.isActive) {
                return Result.failure(Exception("系统状态不能被禁用"))
            }

            val success = taskStatusRepository.update(id, request)
            if (!success) {
                return Result.failure(Exception("更新任务状态失败"))
            }

            val updatedStatus = taskStatusRepository.findById(id)
                ?: return Result.failure(Exception("获取更新后的状态失败"))

            logger.info("更新任务状态成功: ${updatedStatus.name}")
            Result.success(updatedStatus)
        } catch (e: Exception) {
            logger.error("更新任务状态失败", e)
            Result.failure(e)
        }
    }

    /**
     * 删除任务状态
     */
    suspend fun deleteTaskStatus(id: Long): Result<Unit> {
        return try {
            // 检查状态是否存在
            val existingStatus = taskStatusRepository.findById(id)
                ?: return Result.failure(Exception("任务状态不存在"))

            // 系统状态不能删除
            if (existingStatus.isSystem) {
                return Result.failure(Exception("系统状态不能删除"))
            }

            // 检查是否有任务在使用该状态
            val isInUse = taskStatusRepository.isStatusInUse(existingStatus.name)
            if (isInUse) {
                return Result.failure(Exception("该状态正在被任务使用，无法删除"))
            }

            val success = taskStatusRepository.delete(id)
            if (!success) {
                return Result.failure(Exception("删除任务状态失败"))
            }

            logger.info("删除任务状态成功: ${existingStatus.name}")
            Result.success(Unit)
        } catch (e: Exception) {
            logger.error("删除任务状态失败", e)
            Result.failure(e)
        }
    }

    /**
     * 更新状态排序
     */
    suspend fun updateStatusOrder(statusIds: List<Long>): Result<Unit> {
        return try {
            if (statusIds.isEmpty()) {
                return Result.failure(Exception("状态ID列表不能为空"))
            }

            val success = taskStatusRepository.updateSortOrder(statusIds)
            if (!success) {
                return Result.failure(Exception("更新状态排序失败"))
            }

            logger.info("更新状态排序成功")
            Result.success(Unit)
        } catch (e: Exception) {
            logger.error("更新状态排序失败", e)
            Result.failure(e)
        }
    }

    /**
     * 验证状态名称格式
     */
    private fun isValidStatusName(name: String): Boolean {
        return name.matches(Regex("^[A-Z][A-Z0-9_]*$"))
    }
}
