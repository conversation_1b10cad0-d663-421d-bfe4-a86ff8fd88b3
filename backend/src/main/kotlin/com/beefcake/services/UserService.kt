package com.beefcake.services

import com.beefcake.database.tables.UserStatus
import com.beefcake.database.tables.UserType
import com.beefcake.models.*
import com.beefcake.repositories.UserRepository
import com.beefcake.utils.JwtUtils
import com.beefcake.utils.PasswordUtils
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

class UserService {
    private val userRepository = UserRepository()
    private val logger = LoggerFactory.getLogger(UserService::class.java)
    
    companion object {
        const val MAX_LOGIN_ATTEMPTS = 5
        const val LOCKOUT_DURATION_MINUTES = 30L
        const val DEFAULT_RESET_PASSWORD = "1234"
    }
    
    suspend fun register(request: UserCreateRequest): Result<User> {
        // 验证用户名格式
        val usernameValidation = PasswordUtils.validateUsername(request.username)
        if (!usernameValidation.isValid) {
            return Result.failure(Exception(usernameValidation.errors.joinToString("; ")))
        }
        
        // 验证密码强度
        val passwordValidation = PasswordUtils.validatePassword(request.password)
        if (!passwordValidation.isValid) {
            return Result.failure(Exception(passwordValidation.errors.joinToString("; ")))
        }
        
        // 检查用户名是否已存在
        val existingUser = userRepository.findByUsername(request.username)
        if (existingUser != null) {
            return Result.failure(Exception("用户名已存在"))
        }
        
        // 创建用户
        val hashedPassword = PasswordUtils.hashPassword(request.password)
        val user = userRepository.create(request.username, hashedPassword, request.nickname)
            ?: return Result.failure(Exception("用户创建失败"))
        
        logger.info("用户注册成功: ${user.username}")
        return Result.success(user)
    }
    
    suspend fun login(request: UserLoginRequest): Result<UserLoginResponse> {
        val userWithPassword = userRepository.findByUsernameWithPassword(request.username)
            ?: return Result.failure(Exception("用户名或密码错误"))
        
        val (user, hashedPassword) = userWithPassword
        
        // 检查用户状态
        if (user.status == UserStatus.DISABLED) {
            return Result.failure(Exception("账户已被禁用"))
        }
        
        // 检查是否被锁定
        if (user.status == UserStatus.LOCKED) {
            val lockedUntil = user.lockedUntil?.let { LocalDateTime.parse(it) }
            if (lockedUntil != null && LocalDateTime.now().isBefore(lockedUntil)) {
                return Result.failure(Exception("账户已被锁定，请稍后再试"))
            } else if (lockedUntil != null && LocalDateTime.now().isAfter(lockedUntil)) {
                // 解锁账户
                userRepository.updateStatus(user.id, UserStatus.ACTIVE)
                userRepository.resetLoginFailure(user.id)
            }
        }
        
        // 验证密码
        if (!PasswordUtils.verifyPassword(request.password, hashedPassword)) {
            // 增加失败次数
            val newFailedCount = user.failedLoginCount + 1
            if (newFailedCount >= MAX_LOGIN_ATTEMPTS) {
                val lockUntil = LocalDateTime.now().plusMinutes(LOCKOUT_DURATION_MINUTES)
                userRepository.updateLoginFailure(user.id, newFailedCount, lockUntil)
                userRepository.updateStatus(user.id, UserStatus.LOCKED)
                logger.warn("用户 ${user.username} 登录失败次数过多，账户已被锁定")
                return Result.failure(Exception("登录失败次数过多，账户已被锁定${LOCKOUT_DURATION_MINUTES}分钟"))
            } else {
                userRepository.updateLoginFailure(user.id, newFailedCount)
                return Result.failure(Exception("用户名或密码错误"))
            }
        }
        
        // 登录成功，重置失败次数
        userRepository.resetLoginFailure(user.id)
        
        // 生成JWT token
        val token = JwtUtils.generateToken(user.id, user.username, user.userType.name)
        
        logger.info("用户登录成功: ${user.username}")
        return Result.success(UserLoginResponse(token, user))
    }
    
    suspend fun getUserById(userId: Long): User? {
        return userRepository.findById(userId)
    }
    
    suspend fun updateProfile(userId: Long, request: UserUpdateRequest): Result<User> {
        val success = userRepository.updateProfile(userId, request.nickname, request.avatarUrl)
        if (!success) {
            return Result.failure(Exception("更新用户信息失败"))
        }
        
        val updatedUser = userRepository.findById(userId)
            ?: return Result.failure(Exception("获取更新后的用户信息失败"))
        
        logger.info("用户 $userId 更新个人信息成功")
        return Result.success(updatedUser)
    }
    
    suspend fun changePassword(userId: Long, request: PasswordChangeRequest): Result<Unit> {
        val userWithPassword = userRepository.findByIdWithPassword(userId)
            ?: return Result.failure(Exception("用户不存在"))
        
        val (user, currentHashedPassword) = userWithPassword
        
        // 验证旧密码
        if (!PasswordUtils.verifyPassword(request.oldPassword, currentHashedPassword)) {
            return Result.failure(Exception("当前密码错误"))
        }
        
        // 验证新密码强度
        val passwordValidation = PasswordUtils.validatePassword(request.newPassword)
        if (!passwordValidation.isValid) {
            return Result.failure(Exception(passwordValidation.errors.joinToString("; ")))
        }
        
        // 更新密码
        val newHashedPassword = PasswordUtils.hashPassword(request.newPassword)
        val success = userRepository.updatePassword(userId, newHashedPassword)
        
        if (!success) {
            return Result.failure(Exception("密码更新失败"))
        }
        
        logger.info("用户 ${user.username} 修改密码成功")
        return Result.success(Unit)
    }
    
    suspend fun resetPassword(adminUserId: Long, targetUserId: Long): Result<Unit> {
        // 验证管理员权限
        val admin = userRepository.findById(adminUserId)
        if (admin?.userType != UserType.SUPER_ADMIN) {
            return Result.failure(Exception("权限不足"))
        }
        
        val targetUser = userRepository.findById(targetUserId)
            ?: return Result.failure(Exception("目标用户不存在"))
        
        // 重置密码为默认密码
        val hashedPassword = PasswordUtils.hashPassword(DEFAULT_RESET_PASSWORD)
        val success = userRepository.updatePassword(targetUserId, hashedPassword)
        
        if (!success) {
            return Result.failure(Exception("密码重置失败"))
        }
        
        // 重置登录失败次数和解锁账户
        userRepository.resetLoginFailure(targetUserId)
        userRepository.updateStatus(targetUserId, UserStatus.ACTIVE)
        
        logger.info("管理员 ${admin.username} 重置了用户 ${targetUser.username} 的密码")
        return Result.success(Unit)
    }
    
    suspend fun getUserList(page: Int, pageSize: Int): UserListResponse {
        val (users, total) = userRepository.findAll(page, pageSize)
        return UserListResponse(users, total, page, pageSize)
    }

    suspend fun getAllUsers(): List<User> {
        return userRepository.findAllUsers()
    }
}
