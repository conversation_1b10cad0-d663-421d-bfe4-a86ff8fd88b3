package com.beefcake.utils

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.time.ZoneId

object DateTimeUtils {
    
    /**
     * 解析多种格式的时间字符串为LocalDateTime
     * 支持的格式：
     * - ISO格式带时区: 2025-07-20T04:00:00.000Z
     * - ISO本地时间格式: 2025-07-20T04:00:00
     * - 日期时间格式: 2025-07-20 04:00:00
     * - 日期格式: 2025-07-20 (默认时间为00:00:00)
     */
    fun parseDateTime(dateTimeString: String): LocalDateTime {
        return try {
            when {
                // ISO格式带时区: 2025-07-20T04:00:00.000Z
                dateTimeString.contains('T') && dateTimeString.contains('Z') -> {
                    java.time.Instant.parse(dateTimeString)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime()
                }
                // ISO本地时间格式: 2025-07-20T04:00:00
                dateTimeString.contains('T') -> {
                    LocalDateTime.parse(dateTimeString, DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                }
                // 日期时间格式: 2025-07-20 04:00:00
                dateTimeString.contains(' ') && dateTimeString.split(' ').size == 2 -> {
                    LocalDateTime.parse(dateTimeString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                }
                // 日期格式: 2025-07-20
                dateTimeString.matches(Regex("\\d{4}-\\d{2}-\\d{2}")) -> {
                    LocalDateTime.parse("${dateTimeString}T00:00:00", DateTimeFormatter.ISO_LOCAL_DATE_TIME)
                }
                else -> {
                    throw IllegalArgumentException("不支持的时间格式: $dateTimeString")
                }
            }
        } catch (e: Exception) {
            throw IllegalArgumentException("时间格式解析错误: $dateTimeString, 错误信息: ${e.message}")
        }
    }
    
    /**
     * 安全解析时间字符串，返回null如果解析失败
     */
    fun parseDateTimeSafely(dateTimeString: String?): LocalDateTime? {
        return if (dateTimeString.isNullOrBlank()) {
            null
        } else {
            try {
                parseDateTime(dateTimeString)
            } catch (e: Exception) {
                null
            }
        }
    }
}
