package com.beefcake.utils

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.exceptions.JWTVerificationException
import io.ktor.server.config.*
import java.util.*

object JwtUtils {
    private lateinit var secret: String
    private lateinit var issuer: String
    private lateinit var audience: String
    private var expirationTime: Long = 86400000 // 24 hours
    
    fun init(config: ApplicationConfig) {
        val jwtConfig = config.config("jwt")
        secret = jwtConfig.property("secret").getString()
        issuer = jwtConfig.property("issuer").getString()
        audience = jwtConfig.property("audience").getString()
        expirationTime = jwtConfig.property("expirationTime").getString().toLong()
    }
    
    fun generateToken(userId: Long, username: String, userType: String): String {
        return JWT.create()
            .withAudience(audience)
            .withIssuer(issuer)
            .withClaim("userId", userId)
            .withClaim("username", username)
            .withClaim("userType", userType)
            .withExpiresAt(Date(System.currentTimeMillis() + expirationTime))
            .sign(Algorithm.HMAC256(secret))
    }
    
    fun verifyToken(token: String): Map<String, Any>? {
        return try {
            val verifier = JWT.require(Algorithm.HMAC256(secret))
                .withAudience(audience)
                .withIssuer(issuer)
                .build()
            
            val jwt = verifier.verify(token)
            mapOf(
                "userId" to jwt.getClaim("userId").asLong(),
                "username" to jwt.getClaim("username").asString(),
                "userType" to jwt.getClaim("userType").asString()
            )
        } catch (e: JWTVerificationException) {
            null
        }
    }
    
    fun extractUserId(token: String): Long? {
        return verifyToken(token)?.get("userId") as? Long
    }
    
    fun extractUsername(token: String): String? {
        return verifyToken(token)?.get("username") as? String
    }
}
