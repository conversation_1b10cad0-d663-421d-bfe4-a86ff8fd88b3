package com.beefcake.utils

import org.mindrot.jbcrypt.BCrypt

object PasswordUtils {
    private const val SALT_ROUNDS = 12
    
    /**
     * 加密密码
     */
    fun hashPassword(password: String): String {
        return BCrypt.hashpw(password, BCrypt.gensalt(SALT_ROUNDS))
    }
    
    /**
     * 验证密码
     */
    fun verifyPassword(password: String, hashedPassword: String): <PERSON><PERSON>an {
        return BCrypt.checkpw(password, hashedPassword)
    }
    
    /**
     * 验证密码强度
     */
    fun validatePassword(password: String): PasswordValidationResult {
        val errors = mutableListOf<String>()
        
        if (password.length < 8) {
            errors.add("密码长度至少8位")
        }
        
        if (!password.any { it.isLetter() }) {
            errors.add("密码必须包含字母")
        }
        
        if (!password.any { it.isDigit() }) {
            errors.add("密码必须包含数字")
        }
        
        return PasswordValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }
    
    /**
     * 验证用户名格式
     */
    fun validateUsername(username: String): UsernameValidationResult {
        val errors = mutableListOf<String>()
        
        if (username.length < 3) {
            errors.add("用户名长度至少3位")
        }
        
        if (username.length > 50) {
            errors.add("用户名长度不能超过50位")
        }
        
        if (!username.matches(Regex("^[a-zA-Z0-9_]+$"))) {
            errors.add("用户名只能包含英文字母、数字和下划线")
        }
        
        return UsernameValidationResult(
            isValid = errors.isEmpty(),
            errors = errors
        )
    }
}

data class PasswordValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)

data class UsernameValidationResult(
    val isValid: Boolean,
    val errors: List<String>
)
