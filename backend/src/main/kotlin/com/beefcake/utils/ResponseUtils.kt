package com.beefcake.utils

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*
import kotlinx.serialization.Serializable

@Serializable
data class ApiResponse<T>(
    val success: Boolean,
    val message: String,
    val data: T? = null,
    val code: Int = 200
)

@Serializable
data class ErrorResponse(
    val success: Boolean = false,
    val message: String,
    val code: Int,
    val details: String? = null
)

object ResponseUtils {
    suspend inline fun <reified T> ApplicationCall.respondSuccess(
        data: T,
        message: String = "操作成功",
        status: HttpStatusCode = HttpStatusCode.OK
    ) {
        respond(status, ApiResponse(success = true, message = message, data = data, code = status.value))
    }
    
    suspend fun ApplicationCall.respondSuccess(
        message: String = "操作成功",
        status: HttpStatusCode = HttpStatusCode.OK
    ) {
        respond(status, ApiResponse<Unit>(success = true, message = message, code = status.value))
    }
    
    suspend fun ApplicationCall.respondError(
        message: String,
        status: HttpStatusCode = HttpStatusCode.BadRequest,
        details: String? = null
    ) {
        respond(status, ErrorResponse(message = message, code = status.value, details = details))
    }
    
    suspend fun ApplicationCall.respondNotFound(message: String = "资源不存在") {
        respondError(message, HttpStatusCode.NotFound)
    }
    
    suspend fun ApplicationCall.respondUnauthorized(message: String = "未授权访问") {
        respondError(message, HttpStatusCode.Unauthorized)
    }
    
    suspend fun ApplicationCall.respondForbidden(message: String = "权限不足") {
        respondError(message, HttpStatusCode.Forbidden)
    }
    
    suspend fun ApplicationCall.respondValidationError(errors: List<String>) {
        respondError("输入验证失败", HttpStatusCode.BadRequest, errors.joinToString("; "))
    }
    
    suspend fun ApplicationCall.respondInternalError(message: String = "服务器内部错误") {
        respondError(message, HttpStatusCode.InternalServerError)
    }
}
