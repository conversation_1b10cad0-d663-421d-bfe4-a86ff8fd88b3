-- 创建用户表
CREATE TABLE users
(
    id                 BIGINT PRIMARY KEY AUTO_INCREMENT,
    username           VA<PERSON><PERSON><PERSON>(50) UNIQUE NOT NULL COMMENT '用户名',
    password           VARCHAR(255)       NOT NULL COMMENT '密码哈希',
    nickname           VARCHAR(100) COMMENT '昵称',
    avatar_url         VARCHAR(500) COMMENT '头像URL',
    user_type          ENUM('NORMAL', 'SUPER_ADMIN') DEFAULT 'NORMAL' COMMENT '用户类型',
    status             ENUM('ACTIVE', 'DISABLED', 'LOCKED') DEFAULT 'ACTIVE' COMMENT '用户状态',
    failed_login_count INT      DEFAULT 0 COMMENT '登录失败次数',
    locked_until       DATETIME COMMENT '锁定到期时间',
    created_at         DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at         DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建需求表
CREATE TABLE requirements
(
    id                     BIGINT PRIMARY KEY AUTO_INCREMENT,
    title                  VARCHAR(200) NOT NULL COMMENT '需求标题',
    business_description   TEXT         NOT NULL COMMENT '业务描述，面向老板',
    acceptance_criteria    TEXT COMMENT '验收标准',
    status                 ENUM('DRAFT', 'REVIEW', 'APPROVED', 'IN_PROGRESS', 'TESTING', 'DELIVERED', 'REJECTED') DEFAULT 'DRAFT' COMMENT '需求状态',
    priority_importance    ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '重要程度',
    priority_urgency       ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '紧急程度',
    estimated_value        VARCHAR(100) COMMENT '预估价值/收益',
    target_users           VARCHAR(200) COMMENT '目标用户群体',
    business_goal          TEXT COMMENT '业务目标',
    creator_id             BIGINT       NOT NULL COMMENT '创建者ID',
    assignee_id            BIGINT COMMENT '需求负责人ID',
    expected_delivery_date DATETIME COMMENT '期望交付时间',
    actual_delivery_date   DATETIME COMMENT '实际交付时间',
    created_at             DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at             DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users (id),
    FOREIGN KEY (assignee_id) REFERENCES users (id)
);

-- 创建需求附件表
CREATE TABLE requirement_attachments
(
    id             BIGINT PRIMARY KEY AUTO_INCREMENT,
    requirement_id BIGINT       NOT NULL,
    file_name      VARCHAR(255) NOT NULL,
    file_path      VARCHAR(500) NOT NULL,
    file_size      BIGINT       NOT NULL,
    file_type      VARCHAR(100),
    description    VARCHAR(500) COMMENT '附件说明',
    uploaded_by    BIGINT       NOT NULL,
    created_at     DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (requirement_id) REFERENCES requirements (id),
    FOREIGN KEY (uploaded_by) REFERENCES users (id)
);

-- 创建任务表
CREATE TABLE tasks
(
    id                  BIGINT PRIMARY KEY AUTO_INCREMENT,
    title               VARCHAR(200) NOT NULL COMMENT '任务标题',
    description         TEXT COMMENT '技术描述',
    requirement_id      BIGINT       NOT NULL COMMENT '关联需求ID',
    task_type           ENUM('DESIGN', 'DEVELOPMENT', 'TESTING', 'DEPLOYMENT', 'DOCUMENTATION', 'RESEARCH') DEFAULT 'DEVELOPMENT' COMMENT '任务类型',
    status              ENUM('TODO', 'IN_PROGRESS', 'BLOCKED', 'REVIEW', 'TESTING', 'DONE') DEFAULT 'TODO' COMMENT '任务状态',
    priority_importance ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '重要程度',
    priority_urgency    ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM' COMMENT '紧急程度',
    estimated_hours     DECIMAL(5, 2) COMMENT '预估工时',
    actual_hours        DECIMAL(5, 2) COMMENT '实际工时',
    creator_id          BIGINT       NOT NULL COMMENT '创建者ID',
    assignee_id         BIGINT COMMENT '负责人ID',
    due_date            DATETIME COMMENT '截止时间',
    started_at          DATETIME COMMENT '开始时间',
    completed_at        DATETIME COMMENT '完成时间',
    created_at          DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at          DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (requirement_id) REFERENCES requirements (id),
    FOREIGN KEY (creator_id) REFERENCES users (id),
    FOREIGN KEY (assignee_id) REFERENCES users (id)
);

-- 创建任务依赖关系表
CREATE TABLE task_dependencies
(
    id                 BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id            BIGINT NOT NULL COMMENT '当前任务ID',
    depends_on_task_id BIGINT NOT NULL COMMENT '依赖的任务ID',
    dependency_type    ENUM('FINISH_TO_START', 'START_TO_START', 'FINISH_TO_FINISH', 'START_TO_FINISH') DEFAULT 'FINISH_TO_START' COMMENT '依赖类型',
    created_at         DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks (id) ON DELETE CASCADE,
    FOREIGN KEY (depends_on_task_id) REFERENCES tasks (id) ON DELETE CASCADE,
    UNIQUE KEY unique_dependency (task_id, depends_on_task_id)
);

-- 创建任务操作日志表
CREATE TABLE task_logs
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id     BIGINT NOT NULL,
    user_id     BIGINT NOT NULL,
    action_type ENUM('CREATE', 'UPDATE_STATUS', 'UPDATE_ASSIGNEE', 'UPDATE_PRIORITY', 'ADD_COMMENT', 'UPLOAD_ATTACHMENT') NOT NULL,
    old_value   TEXT COMMENT '变更前的值',
    new_value   TEXT COMMENT '变更后的值',
    comment     TEXT COMMENT '操作备注',
    created_at  DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
);

-- 创建任务评论表
CREATE TABLE task_comments
(
    id                BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id           BIGINT NOT NULL,
    user_id           BIGINT NOT NULL,
    content           TEXT   NOT NULL,
    comment_type      ENUM('COMMENT', 'BUG_REPORT') DEFAULT 'COMMENT',
    parent_comment_id BIGINT COMMENT '回复的评论ID',
    created_at        DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks (id),
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (parent_comment_id) REFERENCES task_comments (id)
);

-- 创建任务附件表
CREATE TABLE task_attachments
(
    id          BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_id     BIGINT       NOT NULL,
    file_name   VARCHAR(255) NOT NULL,
    file_path   VARCHAR(500) NOT NULL,
    file_size   BIGINT       NOT NULL,
    file_type   VARCHAR(100),
    uploaded_by BIGINT       NOT NULL,
    created_at  DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks (id),
    FOREIGN KEY (uploaded_by) REFERENCES users (id)
);

-- 创建周计划表
CREATE TABLE weekly_plans
(
    id              BIGINT PRIMARY KEY AUTO_INCREMENT,
    week_start_date DATE   NOT NULL COMMENT '周开始日期',
    week_end_date   DATE   NOT NULL COMMENT '周结束日期',
    plan_name       VARCHAR(200) COMMENT '计划名称',
    status          ENUM('PLANNING', 'IN_PROGRESS', 'COMPLETED') DEFAULT 'PLANNING',
    summary         TEXT COMMENT '周总结',
    created_by      BIGINT NOT NULL,
    created_at      DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at      DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users (id)
);

-- 创建周计划任务关联表
CREATE TABLE weekly_plan_tasks
(
    id                     BIGINT PRIMARY KEY AUTO_INCREMENT,
    weekly_plan_id         BIGINT NOT NULL,
    task_id                BIGINT NOT NULL,
    planned_hours          DECIMAL(5, 2) COMMENT '计划工时',
    is_emergency_insertion BOOLEAN  DEFAULT FALSE COMMENT '是否紧急插入',
    created_at             DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (weekly_plan_id) REFERENCES weekly_plans (id),
    FOREIGN KEY (task_id) REFERENCES tasks (id)
);

-- 创建通知表
CREATE TABLE notifications
(
    id                BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id           BIGINT       NOT NULL,
    title             VARCHAR(200) NOT NULL,
    content           TEXT,
    notification_type ENUM('TASK_ASSIGNED', 'TASK_STATUS_CHANGED', 'TASK_OVERDUE', 'COMMENT_MENTIONED') NOT NULL,
    related_task_id   BIGINT,
    is_read           BOOLEAN  DEFAULT FALSE,
    created_at        DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (related_task_id) REFERENCES tasks (id)
);

-- 创建索引
CREATE INDEX idx_users_username ON users (username);
CREATE INDEX idx_task_logs_task_id ON task_logs (task_id);
CREATE INDEX idx_task_logs_created_at ON task_logs (created_at);

CREATE INDEX idx_requirements_status ON requirements (status);
CREATE INDEX idx_requirements_creator ON requirements (creator_id);
CREATE INDEX idx_requirements_assignee ON requirements (assignee_id);
CREATE INDEX idx_requirements_created_at ON requirements (created_at);

CREATE INDEX idx_tasks_requirement ON tasks (requirement_id);
CREATE INDEX idx_tasks_status ON tasks (status);
CREATE INDEX idx_tasks_type ON tasks (task_type);
CREATE INDEX idx_tasks_creator ON tasks (creator_id);
CREATE INDEX idx_tasks_assignee ON tasks (assignee_id);
CREATE INDEX idx_tasks_created_at ON tasks (created_at);

CREATE INDEX idx_task_dependencies_task ON task_dependencies (task_id);
CREATE INDEX idx_task_dependencies_depends_on ON task_dependencies (depends_on_task_id);

CREATE INDEX idx_notifications_user_id ON notifications (user_id);
CREATE INDEX idx_notifications_is_read ON notifications (is_read);


-- 插入超级管理员用户
-- 密码 'root' 的 BCrypt 哈希值
INSERT INTO users (username, password, nickname, user_type, status, created_at, updated_at)
VALUES ('super', '$2a$12$wJ6N.hK//haPPoBhbtHe6O499jtO66dYN3kHU5ZbdbA1UDiqTmNRu', '超级管理员', 'SUPER_ADMIN', 'ACTIVE',
        NOW(), NOW());


-- 插入示例需求数据
INSERT INTO requirements (title,
                          business_description,
                          acceptance_criteria,
                          status,
                          priority_importance,
                          priority_urgency,
                          estimated_value,
                          target_users,
                          business_goal,
                          creator_id,
                          expected_delivery_date)
VALUES ('用户登录功能优化',
        '为了提升用户体验，需要优化现有的登录流程。用户应该能够快速、安全地登录系统，支持手机号和邮箱两种方式登录。同时需要增加忘记密码功能，让用户能够自助重置密码。',
        '1. 用户可以使用手机号或邮箱登录\n2. 登录时间不超过3秒\n3. 支持忘记密码功能\n4. 登录失败5次后账户锁定30分钟\n5. 支持记住登录状态7天',
        'APPROVED',
        'HIGH',
        'HIGH',
        '提升用户留存率15%，减少客服咨询量20%',
        '所有移动端用户',
        '通过优化登录体验，提高用户满意度和活跃度',
        1,
        DATE_ADD(NOW(), INTERVAL 2 WEEK));

-- 插入示例任务数据（基于上面的需求）
SET
@requirement_id = LAST_INSERT_ID();

INSERT INTO tasks (title, description, requirement_id, task_type, status, priority_importance, priority_urgency,
                   estimated_hours, creator_id, assignee_id, due_date)
VALUES ('登录页面UI设计', '设计新的登录页面，包括手机号/邮箱切换、忘记密码入口等', @requirement_id, 'DESIGN', 'DONE',
        'HIGH', 'HIGH', 8.0, 1, 1, DATE_ADD(NOW(), INTERVAL 3 DAY)),
       ('后端登录接口开发', '开发支持手机号和邮箱登录的后端接口，包括验证逻辑', @requirement_id, 'DEVELOPMENT',
        'IN_PROGRESS', 'HIGH', 'HIGH', 16.0, 1, 1, DATE_ADD(NOW(), INTERVAL 1 WEEK)),
       ('忘记密码功能开发', '开发忘记密码功能，包括发送验证码和重置密码', @requirement_id, 'DEVELOPMENT', 'TODO',
        'MEDIUM', 'HIGH', 12.0, 1,1,  DATE_ADD(NOW(), INTERVAL 10 DAY)),
       ('前端登录页面开发', '根据UI设计实现前端登录页面', @requirement_id, 'DEVELOPMENT', 'TODO', 'HIGH', 'HIGH', 20.0,
        1,1,  DATE_ADD(NOW(), INTERVAL 1 WEEK)),
       ('接口联调测试', '前后端接口联调，确保功能正常', @requirement_id, 'TESTING', 'TODO', 'HIGH', 'MEDIUM', 8.0, 1,1,
        DATE_ADD(NOW(), INTERVAL 12 DAY)),
       ('功能测试', '完整的功能测试，包括各种边界情况', @requirement_id, 'TESTING', 'TODO', 'MEDIUM', 'MEDIUM', 16.0, 1,1,
        DATE_ADD(NOW(), INTERVAL 2 WEEK));

-- 设置任务依赖关系
SET
@design_task_id = (SELECT id FROM tasks WHERE title = '登录页面UI设计' AND requirement_id = @requirement_id);
SET
@backend_task_id = (SELECT id FROM tasks WHERE title = '后端登录接口开发' AND requirement_id = @requirement_id);
SET
@forgot_password_task_id = (SELECT id FROM tasks WHERE title = '忘记密码功能开发' AND requirement_id = @requirement_id);
SET
@frontend_task_id = (SELECT id FROM tasks WHERE title = '前端登录页面开发' AND requirement_id = @requirement_id);
SET
@integration_task_id = (SELECT id FROM tasks WHERE title = '接口联调测试' AND requirement_id = @requirement_id);
SET
@testing_task_id = (SELECT id FROM tasks WHERE title = '功能测试' AND requirement_id = @requirement_id);

-- 插入依赖关系
INSERT INTO task_dependencies (task_id, depends_on_task_id, dependency_type)
VALUES (@frontend_task_id, @design_task_id, 'FINISH_TO_START'),      -- 前端开发依赖UI设计完成
       (@integration_task_id, @backend_task_id, 'FINISH_TO_START'),  -- 联调依赖后端接口完成
       (@integration_task_id, @frontend_task_id, 'FINISH_TO_START'), -- 联调依赖前端页面完成
       (@testing_task_id, @integration_task_id, 'FINISH_TO_START'),  -- 功能测试依赖联调完成
       (@testing_task_id, @forgot_password_task_id, 'FINISH_TO_START'); -- 功能测试依赖忘记密码功能完成

