# API 文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Bearer <PERSON>ken
- **数据格式**: JSON

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "code": 200
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "code": 400,
  "details": "详细错误信息"
}
```

## 认证接口

### 用户注册
- **URL**: `POST /auth/register`
- **描述**: 用户注册
- **请求体**:
```json
{
  "username": "testuser",
  "password": "password123",
  "nickname": "测试用户"
}
```

### 用户登录
- **URL**: `POST /auth/login`
- **描述**: 用户登录
- **请求体**:
```json
{
  "username": "testuser",
  "password": "password123"
}
```
- **响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "testuser",
      "nickname": "测试用户",
      "userType": "NORMAL",
      "status": "ACTIVE"
    }
  }
}
```

## 用户接口

### 获取当前用户信息
- **URL**: `GET /users/me`
- **描述**: 获取当前登录用户信息
- **认证**: 需要
- **响应**: 用户对象

### 更新用户信息
- **URL**: `PUT /users/me`
- **描述**: 更新当前用户信息
- **认证**: 需要
- **请求体**:
```json
{
  "nickname": "新昵称",
  "avatarUrl": "头像URL"
}
```

### 修改密码
- **URL**: `POST /users/change-password`
- **描述**: 修改当前用户密码
- **认证**: 需要
- **请求体**:
```json
{
  "oldPassword": "旧密码",
  "newPassword": "新密码"
}
```

### 获取用户列表（管理员）
- **URL**: `GET /users/list`
- **描述**: 获取用户列表
- **认证**: 需要（超级管理员）
- **查询参数**:
  - `page`: 页码（默认1）
  - `pageSize`: 每页数量（默认20）

### 重置用户密码（管理员）
- **URL**: `POST /users/reset-password`
- **描述**: 重置指定用户密码为1234
- **认证**: 需要（超级管理员）
- **请求体**:
```json
{
  "userId": 2
}
```

## 任务接口（待实现）

### 创建任务
- **URL**: `POST /tasks`
- **描述**: 创建新任务

### 获取任务列表
- **URL**: `GET /tasks`
- **描述**: 获取任务列表

### 更新任务
- **URL**: `PUT /tasks/{id}`
- **描述**: 更新任务信息

### 删除任务
- **URL**: `DELETE /tasks/{id}`
- **描述**: 删除任务

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证说明

1. 登录成功后，服务器返回JWT token
2. 后续请求需要在Header中携带：`Authorization: Bearer {token}`
3. Token有效期为24小时
4. Token过期后需要重新登录
