# 数据库设计文档

## 概述

猛男项目管理系统使用 MySQL 8.0+ 作为主数据库，采用 Exposed ORM 进行数据访问。

## 数据库表结构

### 用户表 (users)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 用户ID |
| username | VARCHAR(50) | UNIQUE, NOT NULL | 用户名 |
| password | VARCHAR(255) | NOT NULL | 密码哈希 |
| nickname | VARCHAR(100) | NULL | 昵称 |
| avatar_url | VARCHAR(500) | NULL | 头像URL |
| user_type | ENUM | DEFAULT 'NORMAL' | 用户类型：NORMAL, SUPER_ADMIN |
| status | ENUM | DEFAULT 'ACTIVE' | 用户状态：ACTIVE, DISABLED, LOCKED |
| failed_login_count | INT | DEFAULT 0 | 登录失败次数 |
| locked_until | DATETIME | NULL | 锁定到期时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

### 任务表 (tasks)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 任务ID |
| title | VARCHAR(200) | NOT NULL | 任务标题 |
| description | TEXT | NULL | 任务描述 |
| requirement_description | TEXT | NULL | 需求描述（面向非技术人员） |
| status | ENUM | DEFAULT 'REQUIREMENT_CREATED' | 任务状态 |
| priority_importance | ENUM | DEFAULT 'MEDIUM' | 重要程度：HIGH, MEDIUM, LOW |
| priority_urgency | ENUM | DEFAULT 'MEDIUM' | 紧急程度：HIGH, MEDIUM, LOW |
| estimated_hours | DECIMAL(5,2) | NULL | 预估工时 |
| actual_hours | DECIMAL(5,2) | NULL | 实际工时 |
| creator_id | BIGINT | NOT NULL, FK | 创建者ID |
| assignee_id | BIGINT | NULL, FK | 负责人ID |
| parent_task_id | BIGINT | NULL, FK | 父任务ID |
| due_date | DATETIME | NULL | 截止时间 |
| started_at | DATETIME | NULL | 开始时间 |
| completed_at | DATETIME | NULL | 完成时间 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

#### 任务状态说明
- `REQUIREMENT_CREATED`: 需求创建
- `TASK_BREAKDOWN`: 分解任务
- `IN_DEVELOPMENT`: 开发中
- `PAUSED`: 暂停
- `TESTING`: 测试
- `PENDING_RELEASE`: 待上线
- `RELEASED`: 上线

### 任务操作日志表 (task_logs)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 日志ID |
| task_id | BIGINT | NOT NULL, FK | 任务ID |
| user_id | BIGINT | NOT NULL, FK | 操作用户ID |
| action_type | ENUM | NOT NULL | 操作类型 |
| old_value | TEXT | NULL | 变更前的值 |
| new_value | TEXT | NULL | 变更后的值 |
| comment | TEXT | NULL | 操作备注 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### 操作类型说明
- `CREATE`: 创建
- `UPDATE_STATUS`: 更新状态
- `UPDATE_ASSIGNEE`: 更新负责人
- `UPDATE_PRIORITY`: 更新优先级
- `ADD_COMMENT`: 添加评论
- `UPLOAD_ATTACHMENT`: 上传附件

### 任务评论表 (task_comments)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 评论ID |
| task_id | BIGINT | NOT NULL, FK | 任务ID |
| user_id | BIGINT | NOT NULL, FK | 评论用户ID |
| content | TEXT | NOT NULL | 评论内容 |
| comment_type | ENUM | DEFAULT 'COMMENT' | 评论类型：COMMENT, BUG_REPORT |
| parent_comment_id | BIGINT | NULL, FK | 回复的评论ID |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 周计划表 (weekly_plans)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 计划ID |
| week_start_date | DATE | NOT NULL | 周开始日期 |
| week_end_date | DATE | NOT NULL | 周结束日期 |
| plan_name | VARCHAR(200) | NULL | 计划名称 |
| status | ENUM | DEFAULT 'PLANNING' | 计划状态：PLANNING, IN_PROGRESS, COMPLETED |
| summary | TEXT | NULL | 周总结 |
| created_by | BIGINT | NOT NULL, FK | 创建者ID |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | DEFAULT CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

### 周计划任务关联表 (weekly_plan_tasks)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 关联ID |
| weekly_plan_id | BIGINT | NOT NULL, FK | 周计划ID |
| task_id | BIGINT | NOT NULL, FK | 任务ID |
| planned_hours | DECIMAL(5,2) | NULL | 计划工时 |
| is_emergency_insertion | BOOLEAN | DEFAULT FALSE | 是否紧急插入 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 任务附件表 (task_attachments)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 附件ID |
| task_id | BIGINT | NOT NULL, FK | 任务ID |
| file_name | VARCHAR(255) | NOT NULL | 文件名 |
| file_path | VARCHAR(500) | NOT NULL | 文件路径 |
| file_size | BIGINT | NOT NULL | 文件大小 |
| file_type | VARCHAR(100) | NULL | 文件类型 |
| uploaded_by | BIGINT | NOT NULL, FK | 上传者ID |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

### 通知表 (notifications)

| 字段名 | 类型 | 约束 | 说明 |
|--------|------|------|------|
| id | BIGINT | PRIMARY KEY, AUTO_INCREMENT | 通知ID |
| user_id | BIGINT | NOT NULL, FK | 用户ID |
| title | VARCHAR(200) | NOT NULL | 通知标题 |
| content | TEXT | NULL | 通知内容 |
| notification_type | ENUM | NOT NULL | 通知类型 |
| related_task_id | BIGINT | NULL, FK | 相关任务ID |
| is_read | BOOLEAN | DEFAULT FALSE | 是否已读 |
| created_at | DATETIME | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

#### 通知类型说明
- `TASK_ASSIGNED`: 任务分配
- `TASK_STATUS_CHANGED`: 任务状态变更
- `TASK_OVERDUE`: 任务逾期
- `COMMENT_MENTIONED`: 评论提及

## 索引设计

```sql
-- 用户表索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_status ON users(status);

-- 任务表索引
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_creator ON tasks(creator_id);
CREATE INDEX idx_tasks_assignee ON tasks(assignee_id);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);

-- 任务日志表索引
CREATE INDEX idx_task_logs_task_id ON task_logs(task_id);
CREATE INDEX idx_task_logs_created_at ON task_logs(created_at);

-- 通知表索引
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
```

## 数据迁移

使用 Flyway 进行数据库版本管理：

- `V1__Create_initial_tables.sql`: 创建初始表结构
- `V2__Insert_super_admin.sql`: 插入超级管理员用户

## 初始数据

系统会自动创建超级管理员账户：
- 用户名: `super`
- 密码: `root`
- 类型: `SUPER_ADMIN`
