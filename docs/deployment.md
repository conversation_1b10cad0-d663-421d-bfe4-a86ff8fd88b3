# 部署指南

## 环境要求

### 开发环境
- JDK 17+
- Node.js 18+
- MySQL 8.0+
- Docker (可选)

### 生产环境
- Docker
- Docker Compose

## 本地开发部署

### 1. 数据库准备

启动 MySQL 数据库：
```bash
# 使用 Docker 启动 MySQL
docker run -d \
  --name beefcake-mysql \
  -e MYSQL_ROOT_PASSWORD=root123 \
  -e MYSQL_DATABASE=beefcake \
  -e MYSQL_USER=beefcake_user \
  -e MYSQL_PASSWORD=beefcake_pass \
  -p 3306:3306 \
  mysql:8.0
```

### 2. 后端启动

```bash
cd backend

# 构建项目
./gradlew build

# 启动应用
./gradlew run
```

后端服务将在 `http://localhost:8080` 启动。

### 3. 前端启动

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:3000` 启动。

## Docker 部署

### 1. 使用 Docker Compose（推荐）

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

服务访问地址：
- 前端: http://localhost:3000
- 后端API: http://localhost:8080
- MySQL: localhost:3306

### 2. 单独构建镜像

#### 构建后端镜像
```bash
cd backend
docker build -t beefcake-backend .
```

#### 构建前端镜像
```bash
cd frontend
docker build -t beefcake-frontend .
```

## 生产环境部署

### 1. 环境变量配置

创建 `.env` 文件：
```bash
# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=beefcake
DB_USER=beefcake_user
DB_PASSWORD=your_secure_password

# JWT 配置
JWT_SECRET=your_super_secret_jwt_key_change_in_production

# 应用配置
APP_ENV=production
UPLOAD_DIR=/app/uploads

# 前端配置
VITE_API_BASE_URL=https://your-domain.com
```

### 2. 生产环境 Docker Compose

```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - beefcake-network

  backend:
    image: beefcake-backend:latest
    environment:
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${DB_NAME}
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      APP_ENV: production
    depends_on:
      - mysql
    restart: unless-stopped
    networks:
      - beefcake-network

  frontend:
    image: beefcake-frontend:latest
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - beefcake-network

volumes:
  mysql_data:

networks:
  beefcake-network:
    driver: bridge
```

### 3. 反向代理配置（Nginx）

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 证书配置
    ssl_certificate /path/to/your/cert.pem;
    ssl_certificate_key /path/to/your/key.pem;
    
    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API 代理
    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 数据备份

### 1. 数据库备份

```bash
# 备份数据库
docker exec beefcake-mysql mysqldump -u root -p beefcake > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复数据库
docker exec -i beefcake-mysql mysql -u root -p beefcake < backup_file.sql
```

### 2. 文件备份

```bash
# 备份上传文件
docker cp beefcake-backend:/app/uploads ./uploads_backup_$(date +%Y%m%d_%H%M%S)
```

## 监控和日志

### 1. 应用日志

```bash
# 查看后端日志
docker logs -f beefcake-backend

# 查看前端日志
docker logs -f beefcake-frontend

# 查看数据库日志
docker logs -f beefcake-mysql
```

### 2. 健康检查

```bash
# 检查后端健康状态
curl http://localhost:8080/health

# 检查前端访问
curl http://localhost:3000
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证数据库连接参数
   - 检查网络连接

2. **JWT Token 无效**
   - 检查 JWT_SECRET 配置
   - 确认 token 未过期

3. **文件上传失败**
   - 检查上传目录权限
   - 验证文件大小限制

4. **前端无法访问后端**
   - 检查 CORS 配置
   - 验证 API 基础URL配置

### 日志级别调整

在 `application.yaml` 中调整日志级别：
```yaml
logging:
  level:
    com.beefcake: DEBUG
    org.springframework.web: DEBUG
```

## 性能优化

### 1. 数据库优化
- 定期分析慢查询
- 优化索引
- 配置连接池参数

### 2. 应用优化
- 启用 JVM 参数调优
- 配置缓存策略
- 优化静态资源

### 3. 前端优化
- 启用 Gzip 压缩
- 配置 CDN
- 优化打包体积
