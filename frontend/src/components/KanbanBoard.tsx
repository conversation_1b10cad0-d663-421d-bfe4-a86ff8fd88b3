import React from 'react'
import { Card, Col, Row, Badge, Empty, Spin } from 'antd'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import type { KanbanData, Task, TaskStatus } from '../types'
import TaskCard from './TaskCard'

interface KanbanBoardProps {
  data: KanbanData | undefined
  loading?: boolean
  onTaskClick?: (task: Task) => void
  onTaskMove?: (taskId: number, newStatus: TaskStatus) => void
}

const KanbanBoard: React.FC<KanbanBoardProps> = ({ 
  data, 
  loading, 
  onTaskClick, 
  onTaskMove 
}) => {
  const columns = [
    {
      id: 'todo',
      title: '待办',
      status: 'TODO' as TaskStatus,
      color: '#d9d9d9',
      tasks: data?.todo || []
    },
    {
      id: 'inProgress',
      title: '进行中',
      status: 'IN_PROGRESS' as TaskStatus,
      color: '#1890ff',
      tasks: data?.inProgress || []
    },
    {
      id: 'blocked',
      title: '阻塞',
      status: 'BLOCKED' as TaskStatus,
      color: '#ff4d4f',
      tasks: data?.blocked || []
    },
    {
      id: 'review',
      title: '评审',
      status: 'REVIEW' as TaskStatus,
      color: '#fa8c16',
      tasks: data?.review || []
    },
    {
      id: 'testing',
      title: '测试',
      status: 'TESTING' as TaskStatus,
      color: '#722ed1',
      tasks: data?.testing || []
    },
    {
      id: 'done',
      title: '完成',
      status: 'DONE' as TaskStatus,
      color: '#52c41a',
      tasks: data?.done || []
    }
  ]

  const handleDragEnd = (result: DropResult) => {
    const { destination, source, draggableId } = result

    // 如果没有目标位置，或者位置没有变化，则不处理
    if (!destination || 
        (destination.droppableId === source.droppableId && 
         destination.index === source.index)) {
      return
    }

    // 找到目标列的状态
    const targetColumn = columns.find(col => col.id === destination.droppableId)
    if (!targetColumn) return

    // 调用回调函数更新任务状态
    const taskId = parseInt(draggableId)
    onTaskMove?.(taskId, targetColumn.status)
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: 50 }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Row gutter={[16, 16]}>
        {columns.map((column) => (
          <Col key={column.id} xs={24} sm={12} md={8} lg={4} xl={4} style={{ marginBottom: 16 }}>
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <div
                    style={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: column.color
                    }}
                  />
                  <span>{column.title}</span>
                  <Badge 
                    count={column.tasks.length} 
                    style={{ backgroundColor: column.color }}
                  />
                </div>
              }
              size="small"
              bodyStyle={{
                padding: 8,
                minHeight: window.innerWidth < 768 ? 300 : 400,
                maxHeight: window.innerWidth < 768 ? 400 : 600,
                overflow: 'auto'
              }}
            >
              <Droppable droppableId={column.id}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.droppableProps}
                    style={{
                      minHeight: 350,
                      backgroundColor: snapshot.isDraggingOver ? '#f0f0f0' : 'transparent',
                      borderRadius: 4,
                      padding: 4,
                      transition: 'background-color 0.2s ease'
                    }}
                  >
                    {column.tasks.length === 0 ? (
                      <Empty 
                        image={Empty.PRESENTED_IMAGE_SIMPLE} 
                        description="暂无任务"
                        style={{ margin: '20px 0' }}
                      />
                    ) : (
                      column.tasks.map((task, index) => (
                        <Draggable
                          key={task.id}
                          draggableId={task.id.toString()}
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              style={{
                                ...provided.draggableProps.style,
                                transform: snapshot.isDragging 
                                  ? provided.draggableProps.style?.transform 
                                  : 'none',
                                opacity: snapshot.isDragging ? 0.8 : 1
                              }}
                            >
                              <TaskCard
                                task={task}
                                onClick={() => onTaskClick?.(task)}
                              />
                            </div>
                          )}
                        </Draggable>
                      ))
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </Card>
          </Col>
        ))}
      </Row>
    </DragDropContext>
  )
}

export default KanbanBoard
