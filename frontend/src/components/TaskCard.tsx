import React from 'react'
import { <PERSON>, Tag, Avatar, Tooltip, Space } from 'antd'
import { 
  ClockCircleOutlined, 
  UserOutlined,
  FlagOutlined,
  LinkOutlined
} from '@ant-design/icons'
import type { Task } from '../types'
import dayjs from 'dayjs'

interface TaskCardProps {
  task: Task
  onClick?: () => void
}

const TaskCard: React.FC<TaskCardProps> = ({ task, onClick }) => {
  const getStatusColor = (status: string) => {
    const colors = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      BLOCKED: 'error',
      REVIEW: 'warning',
      TESTING: 'purple',
      DONE: 'success'
    }
    return colors[status as keyof typeof colors] || 'default'
  }

  const getStatusText = (status: string) => {
    const texts = {
      TODO: '待办',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审',
      TESTING: '测试',
      DONE: '完成'
    }
    return texts[status as keyof typeof texts] || status
  }

  const getPriorityColor = (importance: string, urgency: string) => {
    if (importance === 'HIGH' && urgency === 'HIGH') return '#ff4d4f'
    if (importance === 'HIGH' || urgency === 'HIGH') return '#fa8c16'
    if (importance === 'MEDIUM' || urgency === 'MEDIUM') return '#fadb14'
    return '#52c41a'
  }

  const getPriorityText = (importance: string, urgency: string) => {
    if (importance === 'HIGH' && urgency === 'HIGH') return '紧急重要'
    if (importance === 'HIGH' && urgency === 'MEDIUM') return '重要不紧急'
    if (importance === 'MEDIUM' && urgency === 'HIGH') return '紧急不重要'
    return '一般'
  }

  const isOverdue = task.dueDate && dayjs(task.dueDate).isBefore(dayjs()) && task.status !== 'DONE'

  return (
    <Card
      size="small"
      hoverable
      onClick={onClick}
      style={{
        marginBottom: 8,
        cursor: 'pointer',
        border: isOverdue ? '1px solid #ff4d4f' : undefined,
        backgroundColor: isOverdue ? '#fff2f0' : undefined
      }}
      bodyStyle={{ padding: 12 }}
    >
      <div style={{ marginBottom: 8 }}>
        <div style={{ 
          fontSize: 14, 
          fontWeight: 500, 
          marginBottom: 4,
          lineHeight: '1.4'
        }}>
          {task.title}
        </div>
        {task.description && (
          <div style={{ 
            fontSize: 12, 
            color: '#666',
            lineHeight: '1.3',
            maxHeight: '2.6em',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {task.description}
          </div>
        )}
      </div>

      <Space size={4} wrap style={{ marginBottom: 8 }}>
        <Tag 
          color={getStatusColor(task.status)} 
          size="small"
        >
          {getStatusText(task.status)}
        </Tag>
        
        <Tag 
          color={getPriorityColor(task.priorityImportance, task.priorityUrgency)}
          size="small"
        >
          <FlagOutlined />
          {getPriorityText(task.priorityImportance, task.priorityUrgency)}
        </Tag>

        {task.dependencies && task.dependencies.length > 0 && (
          <Tag size="small" color="orange">
            <LinkOutlined />
            {task.dependencies.length}个依赖
          </Tag>
        )}
      </Space>

      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        fontSize: 12,
        color: '#666'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {task.assignee ? (
            <Tooltip title={`负责人: ${task.assignee.nickname || task.assignee.username}`}>
              <Avatar 
                size={20} 
                src={task.assignee.avatarUrl}
                icon={<UserOutlined />}
              >
                {task.assignee.nickname?.[0] || task.assignee.username[0]}
              </Avatar>
            </Tooltip>
          ) : (
            <Tooltip title="未分配">
              <Avatar size={20} icon={<UserOutlined />} style={{ backgroundColor: '#d9d9d9' }} />
            </Tooltip>
          )}

          {task.estimatedHours && (
            <span>
              <ClockCircleOutlined style={{ marginRight: 2 }} />
              {task.estimatedHours}h
            </span>
          )}
        </div>

        {task.dueDate && (
          <div style={{ 
            color: isOverdue ? '#ff4d4f' : '#666',
            fontWeight: isOverdue ? 500 : 'normal'
          }}>
            {dayjs(task.dueDate).format('MM-DD')}
          </div>
        )}
      </div>

      {task.requirement && (
        <div style={{ 
          marginTop: 8,
          padding: '4px 8px',
          backgroundColor: '#f0f0f0',
          borderRadius: 4,
          fontSize: 11,
          color: '#666'
        }}>
          需求: {task.requirement.title}
        </div>
      )}
    </Card>
  )
}

export default TaskCard
