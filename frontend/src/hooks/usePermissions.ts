import { useAuthStore } from '../stores/authStore'

export const usePermissions = () => {
  const { user } = useAuthStore()

  const isAdmin = () => {
    return user?.userType === 'SUPER_ADMIN'
  }

  const isNormalUser = () => {
    return user?.userType === 'NORMAL'
  }

  const hasPermission = (permission: string) => {
    if (!user) return false

    switch (permission) {
      case 'admin':
        return isAdmin()
      case 'task-status-manage':
        return isAdmin()
      case 'user-manage':
        return isAdmin()
      default:
        return false
    }
  }

  return {
    user,
    isAdmin: isAdmin(),
    isNormalUser: isNormalUser(),
    hasPermission
  }
}
