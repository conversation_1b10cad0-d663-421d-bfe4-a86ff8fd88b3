import React from 'react'
import { Card, Row, Col, Statistic, Typography, Space, Button } from 'antd'
import {
  ProjectOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  FileTextOutlined,
} from '@ant-design/icons'
import { useNavigate } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useAuthStore } from '../stores/authStore'
import { taskApi } from '../services/api'

const { Title, Text } = Typography

const DashboardPage: React.FC = () => {
  const navigate = useNavigate()
  const { user } = useAuthStore()

  // 获取任务统计数据
  const { data: stats, isLoading } = useQuery({
    queryKey: ['taskStatistics'],
    queryFn: taskApi.getTaskStatistics
  })

  const quickActions = [
    {
      title: '创建需求',
      description: '快速创建新的业务需求',
      icon: <PlusOutlined />,
      action: () => navigate('/requirements'),
    },
    {
      title: '需求管理',
      description: '查看和管理所有需求',
      icon: <FileTextOutlined />,
      action: () => navigate('/requirements'),
    },
    {
      title: '创建任务',
      description: '快速创建新的任务',
      icon: <PlusOutlined />,
      action: () => navigate('/tasks'),
    },
    {
      title: '查看四象限',
      description: '按优先级查看任务分布',
      icon: <ProjectOutlined />,
      action: () => navigate('/quadrant'),
    },
    {
      title: '制定周计划',
      description: '规划本周的工作安排',
      icon: <ClockCircleOutlined />,
      action: () => navigate('/weekly-plan'),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>工作台</Title>
        <Text type="secondary">
          欢迎回来，{user?.nickname || user?.username}！
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={isLoading}>
            <Statistic
              title="总任务数"
              value={stats?.totalTasks || 0}
              prefix={<ProjectOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={isLoading}>
            <Statistic
              title="已完成"
              value={stats?.completedTasks || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={isLoading}>
            <Statistic
              title="进行中"
              value={stats?.inProgressTasks || 0}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={isLoading}>
            <Statistic
              title="已逾期"
              value={stats?.overdueTasks || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作" style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          {quickActions.map((action, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card
                hoverable
                onClick={action.action}
                style={{ textAlign: 'center', cursor: 'pointer' }}
              >
                <Space direction="vertical" size="middle">
                  <div style={{ fontSize: 32, color: '#1890ff' }}>
                    {action.icon}
                  </div>
                  <div>
                    <Title level={4} style={{ margin: 0 }}>
                      {action.title}
                    </Title>
                    <Text type="secondary">{action.description}</Text>
                  </div>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 最近任务 */}
      <Card title="最近任务" extra={<Button type="link" onClick={() => navigate('/tasks')}>查看全部</Button>}>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Text type="secondary">暂无任务数据</Text>
          <br />
          <Button type="primary" style={{ marginTop: 16 }} onClick={() => navigate('/tasks')}>
            创建第一个任务
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default DashboardPage
