import React from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  Typography,
  Card,
  Descriptions,
  Tag,
  Button,
  Space,
  List,
  Alert,
  Spin,
  Row,
  Col
} from 'antd'
import {
  ArrowLeftOutlined,
  EditOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FlagOutlined,
  LinkOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import { useQuery } from '@tanstack/react-query'
import { taskApi } from '../services/api'
import type { TaskDependency } from '../types'
import dayjs from 'dayjs'

const { Title, Text } = Typography

const TaskDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const taskId = parseInt(id || '0')

  // 获取任务详情
  const { data: task, isLoading, error } = useQuery({
    queryKey: ['task', taskId],
    queryFn: () => taskApi.getTask(taskId),
    enabled: !!taskId
  })

  const getStatusColor = (status: string) => {
    const colors = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      BLOCKED: 'warning',
      REVIEW: 'purple',
      TESTING: 'cyan',
      DONE: 'success'
    }
    return colors[status as keyof typeof colors] || 'default'
  }

  const getStatusText = (status: string) => {
    const texts = {
      TODO: '待开始',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审中',
      TESTING: '测试中',
      DONE: '已完成'
    }
    return texts[status as keyof typeof texts] || status
  }

  const getPriorityColor = (priority: string) => {
    const colors = {
      HIGH: 'red',
      MEDIUM: 'orange',
      LOW: 'green'
    }
    return colors[priority as keyof typeof colors] || 'default'
  }

  const getPriorityText = (priority: string) => {
    const texts = {
      HIGH: '高',
      MEDIUM: '中',
      LOW: '低'
    }
    return texts[priority as keyof typeof texts] || priority
  }

  const getTaskTypeText = (taskType: string) => {
    const texts = {
      DESIGN: '设计',
      DEVELOPMENT: '开发',
      TESTING: '测试',
      DEPLOYMENT: '部署',
      DOCUMENTATION: '文档',
      RESEARCH: '调研'
    }
    return texts[taskType as keyof typeof texts] || taskType
  }

  const getDependencyTypeText = (dependencyType: string) => {
    const texts = {
      FINISH_TO_START: '完成后开始',
      START_TO_START: '同时开始',
      FINISH_TO_FINISH: '同时完成',
      START_TO_FINISH: '开始后完成'
    }
    return texts[dependencyType as keyof typeof texts] || dependencyType
  }

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  if (error || !task) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Alert
          message="任务不存在"
          description="请检查任务ID是否正确"
          type="error"
          showIcon
        />
        <Button 
          type="primary" 
          style={{ marginTop: 16 }}
          onClick={() => navigate('/tasks')}
        >
          返回任务列表
        </Button>
      </div>
    )
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/tasks')}
          >
            返回
          </Button>
          <Title level={2} style={{ margin: 0 }}>
            任务详情
          </Title>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* 左侧主要信息 */}
        <Col xs={24} lg={16}>
          {/* 基本信息 */}
          <Card 
            title="基本信息" 
            extra={
              <Button 
                type="primary" 
                icon={<EditOutlined />}
                onClick={() => navigate(`/tasks/${taskId}/edit`)}
              >
                编辑
              </Button>
            }
          >
            <Descriptions column={1} bordered>
              <Descriptions.Item label="任务标题">
                <Text strong style={{ fontSize: '16px' }}>
                  {task.title}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="任务描述">
                {task.description || '暂无描述'}
              </Descriptions.Item>
              <Descriptions.Item label="任务类型">
                <Tag color="blue">{getTaskTypeText(task.taskType)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={getStatusColor(task.status)}>
                  {getStatusText(task.status)}
                </Tag>
                {!task.canStart && (
                  <Tag color="warning" icon={<ExclamationCircleOutlined />}>
                    等待依赖任务完成
                  </Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="重要程度">
                <Tag color={getPriorityColor(task.priorityImportance)}>
                  {getPriorityText(task.priorityImportance)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="紧急程度">
                <Tag color={getPriorityColor(task.priorityUrgency)}>
                  {getPriorityText(task.priorityUrgency)}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="预估工时">
                <Space>
                  <ClockCircleOutlined />
                  {task.estimatedHours ? `${task.estimatedHours}小时` : '未设置'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="实际工时">
                <Space>
                  <ClockCircleOutlined />
                  {task.actualHours ? `${task.actualHours}小时` : '未记录'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="负责人">
                <Space>
                  <UserOutlined />
                  {task.assignee?.username || '未分配'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="截止时间">
                {task.dueDate ? dayjs(task.dueDate).format('YYYY-MM-DD HH:mm') : '未设置'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {dayjs(task.createdAt).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {dayjs(task.updatedAt).format('YYYY-MM-DD HH:mm')}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* 关联需求 */}
          {task.requirement && (
            <Card title="关联需求" style={{ marginTop: '24px' }}>
              <Descriptions column={1} bordered>
                <Descriptions.Item label="需求标题">
                  <Text strong>{task.requirement.title}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="业务描述">
                  {task.requirement.businessDescription}
                </Descriptions.Item>
                <Descriptions.Item label="需求状态">
                  <Tag color="blue">{task.requirement.status}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="期望交付时间">
                  {task.requirement.expectedDeliveryDate 
                    ? dayjs(task.requirement.expectedDeliveryDate).format('YYYY-MM-DD')
                    : '未设置'
                  }
                </Descriptions.Item>
              </Descriptions>
            </Card>
          )}
        </Col>

        {/* 右侧依赖关系 */}
        <Col xs={24} lg={8}>
          {/* 上游依赖任务 */}
          <Card 
            title={
              <Space>
                <LinkOutlined />
                上游依赖任务
              </Space>
            }
            size="small"
          >
            {task.dependencies && task.dependencies.length > 0 ? (
              <List
                size="small"
                dataSource={task.dependencies}
                renderItem={(dep: TaskDependency) => (
                  <List.Item>
                    <div style={{ width: '100%' }}>
                      <div>
                        <Text strong>{dep.dependsOnTask?.title}</Text>
                      </div>
                      <div>
                        <Tag color="orange">
                          {getDependencyTypeText(dep.dependencyType)}
                        </Tag>
                        <Tag color={getStatusColor(dep.dependsOnTask?.status || '')}>
                          {getStatusText(dep.dependsOnTask?.status || '')}
                        </Tag>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Text type="secondary">无上游依赖任务</Text>
            )}
          </Card>

          {/* 下游被依赖任务 */}
          <Card 
            title={
              <Space>
                <FlagOutlined />
                下游被依赖任务
              </Space>
            }
            size="small"
            style={{ marginTop: '16px' }}
          >
            {task.dependents && task.dependents.length > 0 ? (
              <List
                size="small"
                dataSource={task.dependents}
                renderItem={(dep: TaskDependency) => (
                  <List.Item>
                    <div style={{ width: '100%' }}>
                      <div>
                        <Text strong>{dep.dependsOnTask?.title}</Text>
                      </div>
                      <div>
                        <Tag color="purple">
                          {getDependencyTypeText(dep.dependencyType)}
                        </Tag>
                        <Tag color={getStatusColor(dep.dependsOnTask?.status || '')}>
                          {getStatusText(dep.dependsOnTask?.status || '')}
                        </Tag>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Text type="secondary">无下游被依赖任务</Text>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default TaskDetailPage
