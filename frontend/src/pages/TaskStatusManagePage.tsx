import React, { useState } from 'react'
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  ColorPicker,
  message,
  Popconfirm,
  Space,
  Tag,
  Tooltip
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DragOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { DragDropContext, Droppable, Draggable, DropResult } from 'react-beautiful-dnd'
import { taskStatusApi } from '../services/api'
import type { 
  TaskStatusModel, 
  TaskStatusCreateRequest, 
  TaskStatusUpdateRequest 
} from '../types'

const TaskStatusManagePage: React.FC = () => {
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingStatus, setEditingStatus] = useState<TaskStatusModel | null>(null)
  const [includeInactive, setIncludeInactive] = useState(false)
  
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 获取任务状态列表
  const { data: statusData, isLoading } = useQuery({
    queryKey: ['taskStatuses', includeInactive],
    queryFn: () => taskStatusApi.getTaskStatuses(includeInactive)
  })

  // 创建任务状态
  const createStatusMutation = useMutation({
    mutationFn: taskStatusApi.createTaskStatus,
    onSuccess: () => {
      message.success('任务状态创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['taskStatuses'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务状态创建失败')
    }
  })

  // 更新任务状态
  const updateStatusMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: TaskStatusUpdateRequest }) =>
      taskStatusApi.updateTaskStatus(id, data),
    onSuccess: () => {
      message.success('任务状态更新成功')
      setIsEditModalVisible(false)
      setEditingStatus(null)
      editForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['taskStatuses'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务状态更新失败')
    }
  })

  // 删除任务状态
  const deleteStatusMutation = useMutation({
    mutationFn: taskStatusApi.deleteTaskStatus,
    onSuccess: () => {
      message.success('任务状态删除成功')
      queryClient.invalidateQueries({ queryKey: ['taskStatuses'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务状态删除失败')
    }
  })

  // 更新排序
  const updateSortMutation = useMutation({
    mutationFn: taskStatusApi.updateStatusOrder,
    onSuccess: () => {
      message.success('排序更新成功')
      queryClient.invalidateQueries({ queryKey: ['taskStatuses'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '排序更新失败')
    }
  })

  const handleCreateStatus = (values: TaskStatusCreateRequest) => {
    createStatusMutation.mutate(values)
  }

  const handleEditStatus = (status: TaskStatusModel) => {
    setEditingStatus(status)
    editForm.setFieldsValue({
      displayName: status.displayName,
      description: status.description,
      color: status.color,
      isActive: status.isActive
    })
    setIsEditModalVisible(true)
  }

  const handleUpdateStatus = (values: TaskStatusUpdateRequest) => {
    if (!editingStatus) return
    updateStatusMutation.mutate({ id: editingStatus.id, data: values })
  }

  const handleDeleteStatus = (id: number) => {
    deleteStatusMutation.mutate(id)
  }

  const handleDragEnd = (result: DropResult) => {
    if (!result.destination || !statusData) return

    const items = Array.from(statusData.statuses)
    const [reorderedItem] = items.splice(result.source.index, 1)
    items.splice(result.destination.index, 0, reorderedItem)

    // 更新排序
    const statusIds = items.map(item => item.id)
    updateSortMutation.mutate({ statusIds })
  }

  const columns = [
    {
      title: '排序',
      dataIndex: 'sortOrder',
      width: 80,
      render: () => <DragOutlined style={{ cursor: 'grab' }} />
    },
    {
      title: '状态名称',
      dataIndex: 'name',
      render: (name: string) => <code>{name}</code>
    },
    {
      title: '显示名称',
      dataIndex: 'displayName'
    },
    {
      title: '颜色',
      dataIndex: 'color',
      width: 100,
      render: (color: string, record: TaskStatusModel) => (
        <Tag color={color}>{record.displayName}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      width: 100,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '类型',
      dataIndex: 'isSystem',
      width: 100,
      render: (isSystem: boolean) => (
        <Tag color={isSystem ? 'blue' : 'default'}>
          {isSystem ? '系统' : '自定义'}
        </Tag>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_: any, record: TaskStatusModel) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditStatus(record)}
            />
          </Tooltip>
          
          {!record.isSystem && (
            <Tooltip title="删除">
              <Popconfirm
                title="确定要删除这个状态吗？"
                description="删除后无法恢复，请谨慎操作。"
                onConfirm={() => handleDeleteStatus(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  size="small"
                  danger
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div style={{ padding: 24 }}>
      <Card
        title="任务状态管理"
        extra={
          <Space>
            <Button
              icon={includeInactive ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={() => setIncludeInactive(!includeInactive)}
            >
              {includeInactive ? '隐藏禁用状态' : '显示禁用状态'}
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsCreateModalVisible(true)}
            >
              新建状态
            </Button>
          </Space>
        }
      >
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="statusList">
            {(provided) => (
              <div ref={provided.innerRef} {...provided.droppableProps}>
                <Table
                  columns={columns}
                  dataSource={statusData?.statuses || []}
                  rowKey="id"
                  loading={isLoading}
                  pagination={false}
                  components={{
                    body: {
                      row: ({ children, ...props }: any) => {
                        const index = statusData?.statuses.findIndex(
                          item => item.id === props['data-row-key']
                        ) ?? 0
                        return (
                          <Draggable
                            key={props['data-row-key']}
                            draggableId={props['data-row-key'].toString()}
                            index={index}
                          >
                            {(provided, snapshot) => (
                              <tr
                                ref={provided.innerRef}
                                {...provided.draggableProps}
                                {...provided.dragHandleProps}
                                {...props}
                                style={{
                                  ...provided.draggableProps.style,
                                  backgroundColor: snapshot.isDragging ? '#f0f0f0' : undefined
                                }}
                              >
                                {children}
                              </tr>
                            )}
                          </Draggable>
                        )
                      }
                    }
                  }}
                />
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </Card>

      {/* 创建状态模态框 */}
      <Modal
        title="新建任务状态"
        open={isCreateModalVisible}
        onCancel={() => {
          setIsCreateModalVisible(false)
          createForm.resetFields()
        }}
        footer={null}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateStatus}
        >
          <Form.Item
            name="name"
            label="状态名称"
            rules={[
              { required: true, message: '请输入状态名称' },
              { pattern: /^[A-Z][A-Z0-9_]*$/, message: '状态名称只能包含大写字母、数字和下划线，且以大写字母开头' }
            ]}
          >
            <Input placeholder="如：CUSTOM_STATUS" />
          </Form.Item>

          <Form.Item
            name="displayName"
            label="显示名称"
            rules={[{ required: true, message: '请输入显示名称' }]}
          >
            <Input placeholder="如：自定义状态" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea placeholder="状态描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="color"
            label="颜色"
            initialValue="#d9d9d9"
          >
            <ColorPicker showText />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsCreateModalVisible(false)
                createForm.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={createStatusMutation.isPending}>
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑状态模态框 */}
      <Modal
        title="编辑任务状态"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false)
          setEditingStatus(null)
          editForm.resetFields()
        }}
        footer={null}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateStatus}
        >
          <Form.Item
            name="displayName"
            label="显示名称"
            rules={[{ required: true, message: '请输入显示名称' }]}
          >
            <Input placeholder="如：自定义状态" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea placeholder="状态描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="color"
            label="颜色"
          >
            <ColorPicker showText />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch 
              checkedChildren="启用" 
              unCheckedChildren="禁用"
              disabled={editingStatus?.isSystem}
            />
          </Form.Item>

          {editingStatus?.isSystem && (
            <div style={{ 
              padding: 8, 
              backgroundColor: '#fff7e6', 
              border: '1px solid #ffd591',
              borderRadius: 4,
              marginBottom: 16,
              fontSize: 12,
              color: '#d46b08'
            }}>
              系统状态不能被禁用
            </div>
          )}

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setIsEditModalVisible(false)
                setEditingStatus(null)
                editForm.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={updateStatusMutation.isPending}>
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TaskStatusManagePage
