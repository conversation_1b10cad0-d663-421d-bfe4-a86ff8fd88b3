import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Typography,
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  message,
  Popconfirm,
  Tabs,
  Row,
  Col
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  AppstoreOutlined,
  TableOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { taskApi, requirementApi, userApi } from '../services/api'
import type { Task, TaskCreateRequest, TaskUpdateRequest, Priority, TaskStatus } from '../types'
import dayjs from 'dayjs'

const { Title } = Typography
const { TextArea } = Input
const { Option } = Select
const { TabPane } = Tabs

const TasksPage: React.FC = () => {
  const navigate = useNavigate()
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingTask, setEditingTask] = useState<Task | null>(null)
  const [selectedRequirementId, setSelectedRequirementId] = useState<number | null>(null)
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const [searchForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<any>({
    page: 1,
    pageSize: 20
  })

  // 获取任务列表
  const { data: taskListData, isLoading } = useQuery({
    queryKey: ['tasks', searchParams],
    queryFn: () => taskApi.getTaskList(searchParams)
  })

  // 获取需求选项
  const { data: requirementOptions } = useQuery({
    queryKey: ['requirement-options'],
    queryFn: () => requirementApi.getRequirementOptions()
  })

  // 获取上游任务选项
  const { data: upstreamTaskOptions } = useQuery({
    queryKey: ['upstream-task-options', selectedRequirementId],
    queryFn: () => taskApi.getUpstreamTaskOptions(selectedRequirementId!),
    enabled: !!selectedRequirementId
  })

  // 获取用户列表
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: () => userApi.getAllUsers()
  })

  // 创建任务
  const createTaskMutation = useMutation({
    mutationFn: taskApi.createTask,
    onSuccess: () => {
      message.success('任务创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务创建失败')
    }
  })

  // 更新任务
  const updateTaskMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: TaskUpdateRequest }) =>
      taskApi.updateTask(id, data),
    onSuccess: () => {
      message.success('任务更新成功')
      setIsEditModalVisible(false)
      setEditingTask(null)
      editForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务更新失败')
    }
  })

  // 删除任务
  const deleteTaskMutation = useMutation({
    mutationFn: taskApi.deleteTask,
    onSuccess: () => {
      message.success('任务删除成功')
      queryClient.invalidateQueries({ queryKey: ['tasks'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '任务删除失败')
    }
  })

  const handleCreateTask = (values: any) => {
    const taskData: TaskCreateRequest = {
      ...values,
      dueDate: values.dueDate ? values.dueDate.toISOString() : undefined,
      dependencies: values.dependencies || []
    }
    createTaskMutation.mutate(taskData)
  }

  const handleRequirementChange = (requirementId: number) => {
    setSelectedRequirementId(requirementId)
    // 清空依赖任务选择
    createForm.setFieldsValue({ dependencies: [] })
  }

  const handleEditTask = (task: Task) => {
    setEditingTask(task)
    editForm.setFieldsValue({
      ...task,
      dueDate: task.dueDate ? dayjs(task.dueDate) : undefined
    })
    setIsEditModalVisible(true)
  }

  // 搜索处理
  const handleSearch = (values: any) => {
    const params = {
      ...values,
      createdAtStart: values.createdAtRange?.[0]?.format('YYYY-MM-DD'),
      createdAtEnd: values.createdAtRange?.[1]?.format('YYYY-MM-DD'),
      page: 1,
      pageSize: searchParams.pageSize
    }
    delete params.createdAtRange
    setSearchParams(params)
  }

  // 重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields()
    setSearchParams({
      page: 1,
      pageSize: 20
    })
  }

  // 分页处理
  const handleTableChange = (pagination: any) => {
    setSearchParams({
      ...searchParams,
      page: pagination.current,
      pageSize: pagination.pageSize
    })
  }

  const handleUpdateTask = (values: any) => {
    if (!editingTask) return

    const taskData: TaskUpdateRequest = {
      ...values,
      dueDate: values.dueDate ? values.dueDate.toISOString() : undefined
    }
    updateTaskMutation.mutate({ id: editingTask.id, data: taskData })
  }

  const handleDeleteTask = (id: number) => {
    deleteTaskMutation.mutate(id)
  }

  const getStatusColor = (status: TaskStatus) => {
    const colors = {
      TODO: 'default',
      IN_PROGRESS: 'processing',
      BLOCKED: 'error',
      REVIEW: 'warning',
      TESTING: 'purple',
      DONE: 'success'
    }
    return colors[status] || 'default'
  }

  const getStatusText = (status: TaskStatus) => {
    const texts = {
      TODO: '待办',
      IN_PROGRESS: '进行中',
      BLOCKED: '阻塞',
      REVIEW: '评审',
      TESTING: '测试',
      DONE: '完成'
    }
    return texts[status] || status
  }

  const getPriorityColor = (priority: Priority) => {
    const colors = {
      HIGH: 'red',
      MEDIUM: 'orange',
      LOW: 'green'
    }
    return colors[priority] || 'default'
  }

  const getPriorityText = (priority: Priority) => {
    const texts = {
      HIGH: '高',
      MEDIUM: '中',
      LOW: '低'
    }
    return texts[priority] || priority
  }

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: TaskStatus) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '重要程度',
      dataIndex: 'priorityImportance',
      key: 'priorityImportance',
      render: (priority: Priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '紧急程度',
      dataIndex: 'priorityUrgency',
      key: 'priorityUrgency',
      render: (priority: Priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '关联需求',
      dataIndex: 'requirement',
      key: 'requirement',
      render: (requirement: any) => requirement ? (
        <span style={{ color: '#1890ff', cursor: 'pointer' }}>
          {requirement.title}
        </span>
      ) : '-',
      ellipsis: true,
    },
    {
      title: '上游依赖',
      dataIndex: 'dependencies',
      key: 'dependencies',
      render: (dependencies: any[]) => {
        if (!dependencies || dependencies.length === 0) return '-'
        return (
          <Tag color="orange">
            {dependencies.length} 个依赖
          </Tag>
        )
      },
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      render: (assignee: any) => assignee ? (
        <span style={{ color: '#1890ff' }}>
          {assignee.nickname}
        </span>
      ) : (
        <span style={{ color: '#999' }}>未分配</span>
      ),
    },
    {
      title: '预估工时',
      dataIndex: 'estimatedHours',
      key: 'estimatedHours',
      render: (hours: number) => hours ? `${hours}h` : '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: Task) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => navigate(`/tasks/${record.id}`)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditTask(record)}
          >
            编辑
          </Button>
          {record.dependents && record.dependents.length > 0 ? (
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
              disabled
              title="该任务被其他任务依赖，无法删除"
            >
              删除
            </Button>
          ) : (
            <Popconfirm
              title="确定要删除这个任务吗？"
              onConfirm={() => handleDeleteTask(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                size="small"
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>任务管理</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsCreateModalVisible(true)}
        >
          创建任务
        </Button>
      </div>

      {/* 搜索筛选区域 */}
      <Card
        title="搜索筛选"
        style={{ marginBottom: 16 }}
        size="small"
      >
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 0 }}
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="search" label="任务标题">
                <Input placeholder="请输入任务标题" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value="TODO">待开始</Option>
                  <Option value="IN_PROGRESS">进行中</Option>
                  <Option value="BLOCKED">阻塞</Option>
                  <Option value="REVIEW">评审中</Option>
                  <Option value="TESTING">测试中</Option>
                  <Option value="DONE">已完成</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="priorityImportance" label="重要程度">
                <Select placeholder="请选择重要程度" allowClear>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="priorityUrgency" label="紧急程度">
                <Select placeholder="请选择紧急程度" allowClear>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="requirementSearch" label="关联需求">
                <Input placeholder="请输入需求标题" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="assigneeSearch" label="负责人">
                <Input placeholder="请输入负责人姓名" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item name="createdAtRange" label="创建时间">
                <DatePicker.RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8} lg={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      <Tabs defaultActiveKey="table">
        <TabPane tab={<span><TableOutlined />列表视图</span>} key="table">
          <Card>
            <Table
              columns={columns}
              dataSource={taskListData?.tasks || []}
              rowKey="id"
              loading={isLoading}
              onChange={handleTableChange}
              pagination={{
                total: taskListData?.total || 0,
                pageSize: searchParams.pageSize || 20,
                current: searchParams.page || 1,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </Card>
        </TabPane>
        <TabPane tab={<span><AppstoreOutlined />看板视图</span>} key="kanban">
          <Card>
            <p>看板视图开发中...</p>
          </Card>
        </TabPane>
      </Tabs>

      {/* 创建任务模态框 */}
      <Modal
        title="创建任务"
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateTask}
        >
          <Form.Item
            name="title"
            label="任务标题"
            rules={[{ required: true, message: '请输入任务标题' }]}
          >
            <Input placeholder="请输入任务标题" />
          </Form.Item>

          <Form.Item
            name="requirementId"
            label="关联需求"
            rules={[{ required: true, message: '请选择关联需求' }]}
          >
            <Select
              placeholder="选择关联需求"
              onChange={handleRequirementChange}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {requirementOptions?.map((requirement) => (
                <Option key={requirement.id} value={requirement.id}>
                  {requirement.title} ({requirement.status})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="taskType"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="选择任务类型">
              <Option value="DESIGN">设计</Option>
              <Option value="DEVELOPMENT">开发</Option>
              <Option value="TESTING">测试</Option>
              <Option value="DEPLOYMENT">部署</Option>
              <Option value="DOCUMENTATION">文档</Option>
              <Option value="RESEARCH">调研</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <div style={{ display: 'flex', gap: 16 }}>
            <Form.Item
              name="priorityImportance"
              label="重要程度"
              style={{ flex: 1 }}
            >
              <Select placeholder="选择重要程度">
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="priorityUrgency"
              label="紧急程度"
              style={{ flex: 1 }}
            >
              <Select placeholder="选择紧急程度">
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item
            name="dependencies"
            label="上游任务依赖"
            help="选择当前任务依赖的其他任务，被依赖的任务完成后当前任务才能开始"
          >
            <Select
              mode="multiple"
              placeholder="选择上游任务（可多选）"
              disabled={!selectedRequirementId}
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {upstreamTaskOptions?.map((task) => (
                <Option key={task.id} value={task.id}>
                  {task.title} ({task.taskType} - {task.status})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div style={{ display: 'flex', gap: 16 }}>
            <Form.Item
              name="estimatedHours"
              label="预估工时（小时）"
              style={{ flex: 1 }}
            >
              <InputNumber min={0} step={0.5} placeholder="预估工时" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="dueDate"
              label="截止时间"
              style={{ flex: 1 }}
            >
              <DatePicker
                showTime
                placeholder="选择截止时间"
                style={{ width: '100%' }}
                format="YYYY-MM-DD HH:mm"
              />
            </Form.Item>
          </div>

          <Form.Item
            name="assigneeId"
            label="负责人"
          >
            <Select
              placeholder="选择负责人（可选）"
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {users?.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createTaskMutation.isPending}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑任务模态框 */}
      <Modal
        title="编辑任务"
        open={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateTask}
        >
          <Form.Item
            name="title"
            label="任务标题"
            rules={[{ required: true, message: '请输入任务标题' }]}
          >
            <Input placeholder="请输入任务标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <Form.Item
            name="requirementDescription"
            label="需求描述（面向非技术人员）"
          >
            <TextArea rows={3} placeholder="请输入需求描述" />
          </Form.Item>

          <Form.Item
            name="status"
            label="任务状态"
          >
            <Select placeholder="选择任务状态">
              <Option value="TODO">待办</Option>
              <Option value="IN_PROGRESS">进行中</Option>
              <Option value="BLOCKED">阻塞</Option>
              <Option value="REVIEW">评审</Option>
              <Option value="TESTING">测试</Option>
              <Option value="DONE">完成</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="assigneeId"
            label="负责人"
          >
            <Select placeholder="选择负责人" allowClear>
              {users?.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <div style={{ display: 'flex', gap: 16 }}>
            <Form.Item
              name="priorityImportance"
              label="重要程度"
              style={{ flex: 1 }}
            >
              <Select placeholder="选择重要程度">
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="priorityUrgency"
              label="紧急程度"
              style={{ flex: 1 }}
            >
              <Select placeholder="选择紧急程度">
                <Option value="HIGH">高</Option>
                <Option value="MEDIUM">中</Option>
                <Option value="LOW">低</Option>
              </Select>
            </Form.Item>
          </div>

          <div style={{ display: 'flex', gap: 16 }}>
            <Form.Item
              name="estimatedHours"
              label="预估工时（小时）"
              style={{ flex: 1 }}
            >
              <InputNumber min={0} step={0.5} placeholder="预估工时" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="actualHours"
              label="实际工时（小时）"
              style={{ flex: 1 }}
            >
              <InputNumber min={0} step={0.5} placeholder="实际工时" style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <Form.Item
            name="dueDate"
            label="截止时间"
          >
            <DatePicker
              showTime
              placeholder="选择截止时间"
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsEditModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateTaskMutation.isPending}
              >
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TasksPage
