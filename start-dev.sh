#!/bin/bash

echo "🚀 启动猛男项目管理系统开发环境"

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "📦 启动 MySQL 数据库..."
docker-compose up -d mysql

echo "⏳ 等待数据库启动..."
sleep 10

# 检查数据库是否启动成功
if docker-compose ps mysql | grep -q "Up"; then
    echo "✅ 数据库启动成功"
else
    echo "❌ 数据库启动失败"
    docker-compose logs mysql
    exit 1
fi

echo "🔧 构建并启动后端服务..."
cd backend

# 检查 Java 是否安装
if ! command -v java &> /dev/null; then
    echo "❌ Java 未安装，请先安装 JDK 17+"
    exit 1
fi

# 构建项目
echo "📦 构建后端项目..."
./gradlew build

if [ $? -eq 0 ]; then
    echo "✅ 后端构建成功"
    echo "🚀 启动后端服务..."
    ./gradlew run &
    BACKEND_PID=$!
    echo "后端进程 PID: $BACKEND_PID"
else
    echo "❌ 后端构建失败"
    exit 1
fi

cd ..

echo "⏳ 等待后端服务启动..."
sleep 15

# 检查后端是否启动成功
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo "🎨 启动前端服务..."
cd frontend

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js 18+"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

echo "🚀 启动前端开发服务器..."
npm run dev &
FRONTEND_PID=$!
echo "前端进程 PID: $FRONTEND_PID"

cd ..

echo "⏳ 等待前端服务启动..."
sleep 10

echo ""
echo "🎉 猛男项目管理系统启动完成！"
echo ""
echo "📱 访问地址："
echo "   前端应用: http://localhost:3000"
echo "   后端API:  http://localhost:8080"
echo "   API文档:  http://localhost:8080/health"
echo ""
echo "👤 默认账户："
echo "   超级管理员: super / root"
echo ""
echo "📝 使用说明："
echo "   1. 访问 http://localhost:3000 打开应用"
echo "   2. 使用 super/root 登录管理员账户"
echo "   3. 或者注册新的普通用户账户"
echo ""
echo "🛑 停止服务："
echo "   Ctrl+C 停止脚本"
echo "   或运行: ./stop-dev.sh"
echo ""

# 创建停止脚本
cat > stop-dev.sh << 'EOF'
#!/bin/bash
echo "🛑 停止猛男项目管理系统..."

# 停止前端和后端进程
pkill -f "npm run dev"
pkill -f "gradlew run"

# 停止数据库
docker-compose down

echo "✅ 所有服务已停止"
EOF

chmod +x stop-dev.sh

# 等待用户中断
trap 'echo ""; echo "🛑 正在停止服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose down; echo "✅ 服务已停止"; exit 0' INT

echo "按 Ctrl+C 停止所有服务"
wait
