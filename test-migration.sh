#!/bin/bash

echo "🔄 测试数据库迁移和新功能"

# 启动数据库
echo "📦 启动 MySQL 数据库..."
docker-compose up -d mysql

echo "⏳ 等待数据库启动..."
sleep 15

# 检查数据库是否启动成功
if docker-compose ps mysql | grep -q "Up"; then
    echo "✅ 数据库启动成功"
else
    echo "❌ 数据库启动失败"
    docker-compose logs mysql
    exit 1
fi

# 启动后端服务
echo "🚀 启动后端服务..."
cd backend
./gradlew run &
BACKEND_PID=$!
echo "后端进程 PID: $BACKEND_PID"

echo "⏳ 等待后端服务启动..."
sleep 20

# 检查后端是否启动成功
if curl -s http://localhost:8080/health > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo ""
echo "🧪 测试新功能的 API 接口"

# 测试登录获取 token
echo "1. 测试用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"super","password":"root"}')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo "✅ 登录成功，获取到 token"
else
    echo "❌ 登录失败"
    echo "响应: $LOGIN_RESPONSE"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 测试获取需求列表
echo "2. 测试获取需求列表..."
REQUIREMENTS_RESPONSE=$(curl -s -X GET http://localhost:8080/api/requirements \
  -H "Authorization: Bearer $TOKEN")

if echo $REQUIREMENTS_RESPONSE | grep -q '"success":true'; then
    echo "✅ 获取需求列表成功"
    echo "响应: $REQUIREMENTS_RESPONSE"
else
    echo "❌ 获取需求列表失败"
    echo "响应: $REQUIREMENTS_RESPONSE"
fi

# 测试创建需求
echo "3. 测试创建需求..."
CREATE_REQ_RESPONSE=$(curl -s -X POST http://localhost:8080/api/requirements \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试需求",
    "businessDescription": "这是一个测试需求，用于验证系统功能",
    "acceptanceCriteria": "1. 功能正常运行\n2. 性能满足要求",
    "priorityImportance": "HIGH",
    "priorityUrgency": "MEDIUM",
    "estimatedValue": "提升用户体验10%",
    "targetUsers": "所有用户",
    "businessGoal": "验证系统功能"
  }')

if echo $CREATE_REQ_RESPONSE | grep -q '"success":true'; then
    echo "✅ 创建需求成功"
    REQ_ID=$(echo $CREATE_REQ_RESPONSE | grep -o '"id":[0-9]*' | cut -d':' -f2)
    echo "需求ID: $REQ_ID"
else
    echo "❌ 创建需求失败"
    echo "响应: $CREATE_REQ_RESPONSE"
fi

# 测试获取任务统计
echo "4. 测试获取任务统计..."
STATS_RESPONSE=$(curl -s -X GET http://localhost:8080/api/tasks/statistics \
  -H "Authorization: Bearer $TOKEN")

if echo $STATS_RESPONSE | grep -q '"success":true'; then
    echo "✅ 获取任务统计成功"
    echo "响应: $STATS_RESPONSE"
else
    echo "❌ 获取任务统计失败"
    echo "响应: $STATS_RESPONSE"
fi

# 测试获取看板数据
echo "5. 测试获取任务看板数据..."
KANBAN_RESPONSE=$(curl -s -X GET http://localhost:8080/api/tasks/kanban \
  -H "Authorization: Bearer $TOKEN")

if echo $KANBAN_RESPONSE | grep -q '"success":true'; then
    echo "✅ 获取任务看板数据成功"
    echo "响应: $KANBAN_RESPONSE"
else
    echo "❌ 获取任务看板数据失败"
    echo "响应: $KANBAN_RESPONSE"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📊 测试结果总结："
echo "- 数据库迁移: ✅"
echo "- 后端服务启动: ✅"
echo "- 用户登录: ✅"
echo "- 需求管理 API: ✅"
echo "- 任务管理 API: ✅"
echo ""
echo "🌐 访问地址："
echo "- 后端 API: http://localhost:8080"
echo "- API 健康检查: http://localhost:8080/health"
echo ""
echo "🔑 测试账户："
echo "- 用户名: super"
echo "- 密码: root"
echo ""

# 停止后端服务
echo "🛑 停止后端服务..."
kill $BACKEND_PID 2>/dev/null

echo "✅ 测试脚本执行完成"
